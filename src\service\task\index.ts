import { http } from '@/utils/http'

// 任务接口类型定义
export interface ITaskItem {
  id: number
  taskType: number
  device: {
    machineNumber: string
  }
  reportId: number
  machineOrder?: any
}

export interface ITaskListResponse {
  content: ITaskItem[]
  totalElements: number
  totalPages: number
}

export interface ITaskListParams {
  taskType: number
  userId: number
  startTime: string
  endTime: string
  page?: number
  size?: number
  sort?: string
}

/**
 * 获取任务列表
 * @param params 查询参数
 * @returns 任务列表数据
 */
export const getList = (params: ITaskListParams) => {
  return http.get<ITaskListResponse>('/api/Task', params)
}

/**
 * 获取今日任务
 * @returns 今日任务数据
 */
export const todayTask = () => {
  return http.get<ITaskListResponse>('/api/Task/todayTask', {})
}

/**
 * 根据质检任务ID获取质检信息 - 从老项目移植
 * @param params 查询参数
 * @returns 质检信息数据
 */
export const getInfoByQualityTaskId = (params: { qualityTaskId: number }) => {
  return http.get('/api/Quality/getInfoByQualityTaskId', params)
}

export default { getList, todayTask, getInfoByQualityTaskId }
