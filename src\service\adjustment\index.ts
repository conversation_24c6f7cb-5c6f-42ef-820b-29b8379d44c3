import { http } from '@/utils/http'
import { IMachineLatestTask, IAdjustmentBind, IAdjustmentRes } from './types'

export const getDeviceMachineTaskInfo = (params: any) => {
  return http.get<IMachineLatestTask>('/api/v1/terminal/adjustment/getDeviceLatestTask', params)
}

export const getPressureMachineTaskInfo = (params: any) => {
  return http.get<IMachineLatestTask>(
    '/api/v1/terminal/adjustment/getPressureDeviceLatestTask',
    params,
  )
}

export const getDevicePartsId = (params: any) => {
  return http.get('/api/v1/terminal/adjustment/getDevicePartsId', params)
}

export const getPressurePartsId = (params: any) => {
  return http.get('/api/v1/terminal/adjustment/getPressurePartsId', params)
}

export const postDeviceParts = (params: IAdjustmentBind) => {
  return http.post('/api/v1/terminal/adjustment/bindDeviceParts', params)
}

export const postPressureParts = (params: IAdjustmentBind) => {
  return http.post('/api/v1/terminal/adjustment/bindPressureParts', params)
}

export const getDeviceAdjustmentList = (params: any) => {
  return http.get<IAdjustmentRes>('/api/v1/terminal/adjustment/getDeviceAdjustmentList', params)
}

export const getPressureAdjustmentList = (params: any) => {
  return http.get<IAdjustmentRes>('/api/v1/terminal/adjustment/getPressureAdjustmentList', params)
}
