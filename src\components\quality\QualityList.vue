<template>
  <view class="quality-list-container">
    <view v-if="loading" class="loading-container">
      <wd-loading color="#2979ff" />
      <text class="loading-text">加载中...</text>
    </view>
    <view v-else-if="list.length > 0" class="list-wrapper">
      <view class="list-container">
        <!-- 列表项 -->
        <view
          v-for="(item, index) in list"
          :key="index"
          class="list-item-wrapper"
          @click="handleItemClick(item)"
        >
          <wd-card class="list-item" shadow="always">
            <template #title>
              <view class="card-header">
                <text class="card-title">{{ getItemTitle(item) }}</text>
                <wd-tag :type="getStatusType(item.status || 0)" class="status-tag">
                  {{ item.statusText || '' }}
                </wd-tag>
              </view>
            </template>

            <view class="card-content">
              <slot name="content" :item="item">
                <!-- 默认内容，可以通过插槽自定义 -->
                <view class="info-list">
                  <view
                    v-for="(field, fieldIndex) in displayFields"
                    :key="fieldIndex"
                    class="info-row"
                  >
                    <text class="info-label">{{ field.label || '' }}:</text>
                    <text class="info-value">{{ getFieldValue(item, field) }}</text>
                  </view>
                </view>
              </slot>
            </view>

            <template #footer>
              <view class="card-footer">
                <text class="item-time">{{ item.inspectionTime || '' }}</text>
              </view>
            </template>
          </wd-card>
        </view>
      </view>
    </view>
    <view v-else class="empty-state">
      <wd-img
        class="empty-img"
        src="/static/images/empty/default-empty.png"
        width="200rpx"
        height="200rpx"
        mode="aspectFit"
      />
      <text class="empty-text">{{ emptyText }}</text>
    </view>
  </view>
</template>

<script lang="ts">
export default {
  name: 'QualityList',
}
</script>

<script lang="ts" setup>
const props = defineProps({
  // 列表数据
  list: {
    type: Array as () => any[],
    required: true,
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false,
  },
  // 空状态文本
  emptyText: {
    type: String,
    default: '暂无数据',
  },
  // 标题字段
  titleField: {
    type: String,
    default: 'materialName', // 默认取materialName作为标题
  },
  // 要显示的字段列表
  displayFields: {
    type: Array as () => any[],
    default: () => [
      { label: '批次号', field: 'batchNo' },
      { label: '数量', field: 'quantity', unit: 'unit' },
      { label: '检验人', field: 'inspector' },
    ],
  },
})

const emit = defineEmits(['item-click'])

// 获取标签类型
const getStatusType = (status: number) => {
  switch (status) {
    case 1: // 合格
      return 'success'
    case 2: // 不合格
      return 'danger'
    default:
      return 'primary'
  }
}

// 获取列表项标题
const getItemTitle = (item: any) => {
  return item[props.titleField] || '未命名项目'
}

// 获取字段值
const getFieldValue = (item: any, field: any) => {
  const value = item[field.field]

  // 如果有单位字段，并且单位字段存在于item中
  if (field.unit && item[field.unit]) {
    return `${value} ${item[field.unit]}`
  }

  return value
}

// 处理列表项点击
const handleItemClick = (item: any) => {
  emit('item-click', item)
}
</script>

<style lang="scss">
.quality-list-container {
  box-sizing: border-box;
  width: 100%;
}

.list-wrapper {
  box-sizing: border-box;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style-type: none;
}

.list-container {
  box-sizing: border-box;
  width: 100%;
  padding: 10rpx;
}

.list-item-wrapper {
  display: flex;
  justify-content: center;
  width: 100%;
  margin-bottom: 10rpx;
}

.list-item {
  width: 100%;
  border-radius: 8rpx;

  :deep(.wd-card__header),
  :deep(.wd-card__content),
  :deep(.wd-card__footer) {
    box-sizing: border-box;
    width: 100%;
    padding: 16rpx;
  }

  :deep(.wd-card__body) {
    width: 100%;
    padding: 0;
    margin: 0;
  }
}

.card-header {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.card-title {
  flex: 1;
  min-width: 0;
  margin-right: 20rpx;
  overflow: hidden;
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.status-tag {
  flex-shrink: 0;
  min-width: 70rpx;
  margin-left: auto;
  text-align: center;
}

.card-content {
  box-sizing: border-box;
  width: 100%;
  padding: 10rpx 0;
}

.info-list {
  width: 100%;
}

.info-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
  list-style-type: none;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  flex-shrink: 0;
  width: 180rpx;
  font-size: 28rpx;
  color: #666666;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
  word-break: break-all;
}

.card-footer {
  box-sizing: border-box;
  display: flex;
  justify-content: flex-end;
  width: 100%;
}

.item-time {
  font-size: 26rpx;
  color: #999999;
}

.empty-state,
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-img {
  margin-bottom: 20rpx;
}

.empty-text,
.loading-text {
  font-size: 28rpx;
  color: #999999;
}
/* 响应式媒体查询 */
@media screen and (max-width: 375px) {
  .info-label {
    width: 100rpx;
  }
}
</style>
