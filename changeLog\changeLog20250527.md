# 变更日志 - 2025年5月27日

## [16:56:49]: 添加委外加工检验功能模块

**作者**: 系统用户  
**文件**:

- src/pages/quality/outsourced-inspection/index.vue
- src/pages/quality/outsourced-inspection/form.vue
- src/pages/quality/outsourced-inspection/detail.vue

### 变更描述

参照来料检验模块，新增委外加工检验功能，包括列表页、新增表单和详情页。表单页面根据用户提供的图片进行了实现，只包含检验人员、数量、批次码、产品名称、委外加工公司、工艺等字段。

### 变更详情

新增了三个页面文件：

1. 列表页面 - 展示委外加工检验记录
2. 表单页面 - 新增委外加工检验
3. 详情页面 - 查看委外加工检验详情

所有页面遵循项目的UI规范和代码风格，与已有的来料检验模块保持一致的用户体验。

## [16:33:32] feat: 完善来料检验搜索功能和添加详情页面

**作者**: 开发者  
**文件**:

- src/pages/quality/incoming-inspection/index.vue
- src/pages/quality/incoming-inspection/detail.vue

### 变更描述

1. 为来料检验列表页添加搜索功能实现
2. 完善检验列表项点击事件，实现查看详情功能
3. 创建来料检验详情页面，展示完整的检验记录信息

### 变更详情

**原代码 (index.vue)**:

```vue
<wd-input v-model="searchKeyword" placeholder="请输入搜索关键词" clearable prefix-icon="search" />

// 查看详情 const viewDetail = (item: any) => { console.log('查看详情:', item) //
实际项目中应该跳转到详情页面 }
```

**新代码 (index.vue)**:

```vue
<wd-input
  v-model="searchKeyword"
  placeholder="请输入搜索关键词"
  clearable
  prefix-icon="search"
  @input="handleSearch"
/>

// 查看详情 const viewDetail = (item: any) => { console.log('查看详情:', item) // 跳转到详情页面
uni.navigateTo({ url: `/pages/quality/incoming-inspection/detail?id=${item.id}`, fail: (err) => {
console.error('导航失败:', err) uni.showToast({ title: '页面开发中...', icon: 'none' }) } }) } //
处理搜索 const handleSearch = () => { // 实现搜索逻辑 loading.value = true // 模拟搜索过滤
setTimeout(() => { if (!searchKeyword.value) { // 搜索词为空，恢复全部数据 loadData() return } //
基于关键词过滤数据 const originalList = [/* 省略原始数据 */] const keyword =
searchKeyword.value.toLowerCase() const filteredList = originalList.filter(item =>
item.materialName.toLowerCase().includes(keyword) || item.batchNo.toLowerCase().includes(keyword) ||
item.inspector.includes(keyword) ) incomingList.value = filteredList loading.value = false }, 300) }
```

**新增文件 (detail.vue)**:

```vue
<!-- 新创建的详情页面 -->
<route lang="json5">
{
  needLogin: true,
  style: {
    navigationBarTitleText: '检验详情',
  },
}
</route>

<template>
  <view class="container">
    <view v-if="loading" class="loading-container">
      <text class="loading-text">加载中...</text>
    </view>
    <view v-else class="detail-card">
      <!-- 标题和状态标签 -->
      <view class="card-header">
        <text class="card-title">{{ detailData.materialName }}</text>
        <wd-tag :type="getStatusType(detailData.status)">{{ detailData.statusText }}</wd-tag>
      </view>

      <!-- 基本信息、检验结果、不良项目、备注信息、检验图片等内容... -->
    </view>
  </view>
</template>

<script setup lang="ts">
// 详情页面逻辑实现
// 包括获取ID参数、获取详情数据、预览图片等功能
</script>
```

# 变更日志

## [2024-05-27 16:00:00] feat: 添加首页功能模块点击跳转功能

**作者**: 系统开发者  
**文件**: src/pages/index/index.vue

### 变更描述

为首页的功能模块添加点击跳转功能，使用户能够通过点击功能模块进入对应的功能页面。

### 变更详情

1. 为功能模块添加点击事件处理函数
2. 为每个功能模块添加URL属性，指定跳转路径
3. 添加错误处理，当页面不存在时给出友好提示

**原代码**:

```javascript
// 定义质量角色专属功能列表
const qualityFunctionList = [
  { name: '来料检', icon: 'search', bgColor: '#fa3534' },
  { name: '委外加工检', icon: 'search', bgColor: '#ff9900' },
  { name: '完工检', icon: 'search', bgColor: '#19be6b' },
]

// 定义普通功能列表
const normalFunctionList = [
  { name: '排班', icon: 'calendar', bgColor: '#2979ff' },
  { name: '领料', icon: 'download', bgColor: '#19be6b' },
  { name: '调机', icon: 'tools', bgColor: '#4c6ef5' },
  { name: '报工', icon: 'clock', bgColor: '#ff9900' },
  { name: '质检', icon: 'search', bgColor: '#fa3534' },
  { name: '生产进度', icon: 'chart', bgColor: '#1296db' },
  { name: '消息', icon: 'notification', bgColor: '#9c27b0' },
]
```

**新代码**:

```javascript
// 定义质量角色专属功能列表
const qualityFunctionList = [
  { name: '来料检', icon: 'search', bgColor: '#fa3534', url: '/pages/quality/incoming-inspection/index' },
  { name: '委外加工检', icon: 'search', bgColor: '#ff9900', url: '/pages/quality/outsourced-inspection/index' },
  { name: '完工检', icon: 'search', bgColor: '#19be6b', url: '/pages/quality/completion-inspection/index' },
]

// 定义普通功能列表
const normalFunctionList = [
  { name: '排班', icon: 'calendar', bgColor: '#2979ff', url: '/pages/arrangement/index' },
  { name: '领料', icon: 'download', bgColor: '#19be6b', url: '/pages/material/pickup' },
  { name: '调机', icon: 'tools', bgColor: '#4c6ef5', url: '/pages/machine/setup' },
  { name: '报工', icon: 'clock', bgColor: '#ff9900', url: '/pages/report/work' },
  { name: '质检', icon: 'search', bgColor: '#fa3534', url: '/pages/quality/index' },
  { name: '生产进度', icon: 'chart', bgColor: '#1296db', url: '/pages/production/progress' },
  { name: '消息', icon: 'notification', bgColor: '#9c27b0', url: '/pages/message/index' },
]

// 处理模块点击事件
const handleModuleClick = (item: any) => {
  console.log('点击功能模块:', item.name)

  // 检查是否有定义URL
  if (item.url) {
    uni.navigateTo({
      url: item.url,
      fail: (err) => {
        console.error('导航失败:', err)
        // 如果导航失败，可能是页面不存在，提示用户
        uni.showToast({
          title: '功能开发中...',
          icon: 'none'
        })
      }
    })
  } else {
    uni.showToast({
      title: '功能开发中...',
      icon: 'none'
    })
  }
}
```

**HTML模板变更**:

```html
<!-- 功能模块入口 -->
<view class="function-grid">
  <view
    class="grid-item"
    v-for="(item, index) in functionList"
    :key="index"
    @click="handleModuleClick(item)"
  >
    <view class="icon-wrapper" :style="{ backgroundColor: item.bgColor }">
      <wd-icon :name="item.icon" size="28" color="#FFFFFF" />
      <view v-if="item.name === '消息' && unreadCount > 0" class="badge">{{ unreadCount }}</view>
    </view>
    <text class="grid-text">{{ item.name }}</text>
  </view>
</view>
```

## [2024-05-27 15:30:00] feat: 添加来料检验模块页面

**作者**: 系统开发者  
**文件**:

- src/pages/quality/incoming-inspection/index.vue
- src/pages/quality/incoming-inspection/form.vue
- .cursor/rules/ui-framework.mdc

### 变更描述

1. 添加来料检验列表页面，展示来料检验记录
2. 添加来料检验表单页面，用于提交来料检验数据
3. 更新UI规则文件，添加质量检验模块相关的UI规范

### 变更详情

**新增页面**:

1. 来料检验列表页面 (src/pages/quality/incoming-inspection/index.vue)

   - 使用z-paging组件实现列表的下拉刷新和上拉加载
   - 展示来料检验记录，包括材料名称、批次号、数量、不良品数量等信息
   - 底部固定"新增来料检验"按钮

2. 来料检验表单页面 (src/pages/quality/incoming-inspection/form.vue)
   - 实现与图片一致的表单布局
   - 包含检验人员、来料名称、批次号、单位、数量、不良品数量、取证查询、备注等字段
   - 支持图片上传功能

**更新UI规则**:

在.cursor/rules/ui-framework.mdc文件中添加了：

- 通用页面模式（列表页面和表单页面）
- 质量检验模块的UI规范
- 色彩规范
- 来料检验相关页面的UI设计规范

## [2024-05-27 15:23:38] feat: 添加基于角色ID的功能列表过滤

**作者**: 系统开发者  
**文件**: src/pages/index/index.vue

### 变更描述

根据用户角色ID过滤首页功能列表，为质量部门(ID=18)角色用户提供专属功能列表，包含来料检、委外加工检、完工检三个功能项。

### 变更详情

**原代码**:

```javascript
// 根据角色显示不同的功能列表
const functionList = computed(() => {
  // 检查用户是否具有质量角色
  const isQualityRole = userStore.userInfo?.roles?.some((role) => role === 'ROLE_QUALITY')
  return isQualityRole ? qualityFunctionList : normalFunctionList
})
```

**新代码**:

```javascript
// 根据角色显示不同的功能列表
const functionList = computed(() => {
  // 检查用户是否具有质量角色（ID为18的质量部角色）
  const isQualityRole = userStore.userInfo?.user?.roles?.[0]?.id === 18
  return isQualityRole ? qualityFunctionList : normalFunctionList
})
```
