import { http } from '@/utils/http'
import { IMessageParams, IMessageRes } from './types'

export const getMessageList = (data: IMessageParams) => {
  return http.get<IMessageRes>('/api/v1/terminal/message/getMessageList', data)
}

export const getUnreadMessageCount = (readStatus: number) => {
  return http.get<number>('/api/v1/terminal/message/getUnreadMessageCount', { readStatus })
}

export const confirmMessageTask = (messageId: number) => {
  return http.post<boolean>('/api/v1/terminal/message/confirmTask', { messageId })
}
