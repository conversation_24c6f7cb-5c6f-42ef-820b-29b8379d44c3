# 变更日志 - 2025年06月24日

## 功能新增

### 来料检管理页面 - 追溯码打印功能

**文件路径**: `src/pages/quality/incoming/index.vue`

**变更描述**: 在来料检管理页面的追溯码卡片标题右侧添加打印按钮，用户可以点击打印按钮来打印追溯码标签。

#### 主要变更内容

1. **卡片标题结构调整**
   - 将原来的简单标题改为自定义标题模板
   - 使用 `template #title` 插槽自定义标题内容
   - 添加 flex 布局来放置标题文本和打印按钮

2. **打印按钮实现**
   - 添加带有打印机图标的小型按钮
   - 使用 `@click.stop` 阻止事件冒泡，避免触发卡片点击事件
   - 按钮样式为主色调的线框按钮（`type="primary" plain`）

3. **打印功能逻辑**
   - 实现 `handlePrint` 函数处理打印按钮点击事件
   - 添加打印确认对话框，提升用户体验
   - 实现 `performPrint` 函数执行实际打印操作
   - 当前显示成功提示，预留后续集成实际打印API的接口

4. **样式优化**
   - 添加 `.card-header` 样式实现标题和按钮的水平布局
   - 添加 `.card-title` 样式设置标题文本样式
   - 添加 `.print-btn` 样式优化打印按钮外观
   - 使用 `:deep()` 深度选择器调整按钮内部文本布局

#### 代码变更详情

**原代码**:
```vue
<wd-card :title="'追溯码: ' + item.traceCode" type="rectangle">
  <wd-cell title="物料名称" :value="item.materialName" />
  <!-- 其他cell内容 -->
</wd-card>
```

**新代码**:
```vue
<wd-card type="rectangle">
  <!-- 自定义标题，包含追溯码和打印按钮 -->
  <template #title>
    <view class="card-header">
      <text class="card-title">追溯码: {{ item.traceCode }}</text>
      <wd-button 
        type="primary" 
        size="small" 
        plain 
        @click.stop="handlePrint(item)"
        class="print-btn"
      >
        <wd-icon name="printer" size="14" />
        打印
      </wd-button>
    </view>
  </template>
  
  <wd-cell title="物料名称" :value="item.materialName" />
  <!-- 其他cell内容 -->
</wd-card>
```

**新增JavaScript函数**:
```typescript
// 打印追溯码标签
const handlePrint = (item: IIncomingItem) => {
  // 阻止事件冒泡，防止触发卡片点击事件
  console.log('打印追溯码:', item.traceCode)
  
  // 显示打印确认提示
  uni.showModal({
    title: '打印确认',
    content: `确定要打印追溯码 "${item.traceCode}" 的标签吗？`,
    success: (res) => {
      if (res.confirm) {
        // 执行打印逻辑
        performPrint(item)
      }
    }
  })
}

// 执行打印操作
const performPrint = (item: IIncomingItem) => {
  // 这里可以调用打印机API或者生成打印数据
  // 目前先显示成功提示
  uni.showToast({
    title: '打印任务已发送',
    icon: 'success',
    duration: 2000
  })
  
  // TODO: 实际的打印逻辑
  // 可能需要调用后端API生成打印数据
  // 或者调用设备的打印接口
}
```

**新增CSS样式**:
```scss
/* 卡片标题样式 */
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  flex: 1;
}

/* 打印按钮样式 */
.print-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx !important;
  font-size: 24rpx !important;
  border-radius: 8rpx !important;
  
  :deep(.wd-button__text) {
    display: flex;
    align-items: center;
    gap: 8rpx;
  }
}
```

#### 技术要点

1. **组件插槽使用**: 利用 wot-design-uni 卡片组件的 `#title` 插槽实现自定义标题布局
2. **事件处理**: 使用 `@click.stop` 阻止事件冒泡，确保打印按钮点击不会触发卡片的其他点击事件
3. **图标集成**: 使用 wot-design-uni 的图标组件显示打印机图标
4. **用户体验**: 添加确认对话框和成功提示，提升操作的友好性
5. **样式深度选择器**: 使用 Vue3 的 `:deep()` 语法调整组件内部样式

#### 后续优化建议

1. **集成实际打印API**: 将 `performPrint` 函数中的 TODO 部分替换为实际的打印接口调用
2. **打印模板设计**: 根据业务需求设计追溯码标签的打印模板
3. **错误处理**: 添加打印失败的错误处理逻辑
4. **打印历史**: 考虑添加打印历史记录功能

#### 测试建议

1. 测试打印按钮的点击响应
2. 验证确认对话框的显示和交互
3. 确认事件冒泡被正确阻止
4. 检查在不同屏幕尺寸下的布局表现
5. 测试打印功能的用户体验流程

---

## UI优化

### 排班任务页面 (tasks.vue) - 使用wot ui组件重构

**文件路径**: `src/pages/arrangement/tasks.vue`

**变更描述**: 对排班任务页面进行了全面的UI优化，使用wot ui组件库重构，实现简约现代的设计风格，提升用户体验。

#### 主要变更内容

1. **页面结构重构**
   - 使用 `wd-card` 组件替代传统div布局
   - 采用 `wd-cell-group` 和 `wd-cell` 组件优化表单展示
   - 添加卡片标题和图标，提升视觉层次

2. **设备网格优化**
   - 使用CSS Grid布局替代Flexbox，实现更好的响应式效果
   - 为每个设备卡片添加状态图标和标签
   - 实现渐变背景和边框颜色区分不同状态
   - 添加点击动画效果和状态视觉反馈

3. **新增辅助方法**
   - `formatDateRange()`: 格式化日期范围显示
   - `getDeviceStatusClass()`: 获取设备状态样式类
   - `getDeviceIcon()`: 获取设备状态图标
   - `getDeviceIconColor()`: 获取设备图标颜色
   - `getDeviceDisplayName()`: 获取设备显示名称
   - `getDeviceTagType()`: 获取设备标签类型
   - `getDeviceStatusText()`: 获取设备状态文本

4. **样式系统重构**
   - 使用SCSS和项目设计系统变量
   - 实现响应式网格布局
   - 添加设备状态的视觉区分
   - 优化空状态展示

#### 设备状态分类

- `device-active`: 正常排班设备（绿色边框和渐变背景）
- `device-idle`: 未排班设备（灰色边框和渐变背景）
- `device-error`: 异常任务设备（橙色边框和渐变背景）
- `device-disabled`: 停用设备（红色边框和渐变背景，半透明）

#### 响应式设计

```scss
@media (max-width: 750rpx) {
  .device-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 600rpx) {
  .device-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
```

#### 优化效果

1. **视觉效果提升**: 使用卡片布局和统一的设计语言，页面更加现代化和专业
2. **交互体验改善**: 添加了图标、状态标签和动画效果，提升用户操作反馈
3. **信息层次清晰**: 通过颜色和图标区分不同设备状态，信息一目了然
4. **响应式适配**: 支持不同屏幕尺寸的良好显示效果
5. **代码可维护性**: 使用设计系统变量和组件化开发，便于后续维护

#### 技术要点

- 使用wot-design-uni组件库的wd-card、wd-cell、wd-tag等组件
- 采用项目设计系统的颜色、间距、字体等规范
- 实现CSS Grid布局的响应式设计
- 添加详细的中文注释说明各个功能模块
- 保持原有业务逻辑不变，只优化UI展示

---

**变更人员**: AI Assistant
**变更时间**: 2025-06-24
**影响范围**: 来料检管理页面用户界面和交互功能、排班任务页面UI优化
**风险评估**: 低风险，仅涉及UI展示和用户交互优化
