<route lang="json5">
{
  layout: 'default',
  needLogin: true,
  style: {
    navigationBarTitleText: '排班详情',
  },
}
</route>
<template>
  <view class="container">
    <!-- 顶部导航 -->

    <!-- 基础信息卡片 -->
    <wd-card class="info-card">
      <view class="info-content">
        <wd-cell title="加工计划" :value="schMachine.machinePlanNum" />
        <wd-cell title="加工单" :value="schMachine.machineOrderNum" />
        <wd-cell title="班次" :value="schMachine.shift" />
        <view style="display: flex; justify-content: flex-end">
          <wd-img
            v-if="schMachine.taskStatus === 2"
            :width="47"
            :height="47"
            :src="abnormalImage"
          />
        </view>
      </view>
    </wd-card>

    <!-- 工单信息卡片 -->
    <wd-card class="work-order-card">
      <template #title>
        <view class="card-header">
          <text class="section-title">工单信息</text>
          <wd-tag v-if="schMachine.scheduleExist" type="primary" plain>已排班</wd-tag>
          <wd-tag v-if="!schMachine.scheduleExist" type="primary" plain>待排班</wd-tag>
        </view>
      </template>
      <view class="work-order-content">
        <wd-cell title="生产机台" :value="schMachine.machineNum" />
        <wd-cell title="排程时间" :value="schMachine.scheduleDate" />
        <wd-cell title="产品名称" :value="schMachine.materialName" />
        <wd-cell title="工序" :value="schMachine.procedureName" />
        <wd-cell title="计划数量" :value="schMachine.planQuantity" />
        <wd-cell
          v-if="schMachine.scheduleExist"
          title="料号"
          :value="schMachine.taskMaterialCode"
        />
        <wd-cell title="备注" :value="schMachine.remark" />
      </view>
    </wd-card>

    <!-- 排班操作卡片 -->
    <wd-card class="schedule-card">
      <template #header>
        <text class="section-title">排班</text>
      </template>
      <view class="form-content">
        <wd-picker
          :columns="workTypeColumns"
          label="工种"
          v-model="workType"
          required
          @confirm="handleWorkTypeConfirm"
        />
        <view class="flex items-center">
          <wd-select-picker
            type="radio"
            v-model="selectedWorker"
            :columns="workerOptions"
            :label="workTypeColumns[workType].label"
            :placeholder="'请选择' + workTypeColumns[workType].label"
            required
            filterable
            filter-placeholder="请输入选择"
            @confirm="handleWorkerConfirm"
            class="flex-1"
          ></wd-select-picker>
        </view>
      </view>
    </wd-card>

    <!-- 底部按钮 -->
    <view class="button-group">
      <wd-button
        v-if="!schMachine.scheduleExist"
        :disabled="schMachine.status === 2"
        type="success"
        size="large"
        @click="handleAdd"
      >
        提交
      </wd-button>
      <wd-button v-if="schMachine.scheduleExist" type="primary" size="large" @click="handleModify">
        修改
      </wd-button>
      <wd-button
        v-if="schMachine.taskStatus === 2"
        type="primary"
        size="large"
        plain
        @click="cancelAbnormalTask"
      >
        取消异常标记
      </wd-button>
      <wd-button
        v-else
        :disabled="schMachine.status === 2"
        type="warning"
        size="large"
        plain
        @click="handleException"
      >
        异常标记
      </wd-button>
      <wd-message-box />
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import {
  getUerRoleName,
  checkSchedule,
  addSchedule,
  editSchedule,
  abnormal,
  unabnormal,
} from '@/service/arrangement/arrangement'
import { ColumnItem } from 'wot-design-uni/components/wd-picker-view/types'
import { useMessage, useToast } from 'wot-design-uni'
import abnormalImage from '@/static/images/abnormal.png'

const toast = useToast()

const message = useMessage()
// 响应式数据
const workType = ref(0)
const workTypeColumns = ref<ColumnItem[]>([
  {
    value: 0,
    label: '作业员',
    disabled: false,
  },
  {
    value: 1,
    label: '临时工',
    disabled: false,
  },
])
const selectedWorker = ref<number | string | boolean>()

// 作业员选项
const workerOptions = ref<ColumnItem[]>([])

const schMachine = ref()
const loading = ref(false)
const remark = ref<string | number>()

onShow(() => {
  console.log('onShow')
  schMachine.value = uni.getStorageSync('schMachine')
  if (schMachine.value.scheduleExist) {
    workType.value = schMachine.value.personType
    if (workType.value === 0) {
      getUserListData('操作工')
    } else if (workType.value === 1) {
      getUserListData('临时工')
    }
    selectedWorker.value = schMachine.value.userId
  } else {
    getUserListData('操作工')
  }
})
// 事件处理函数
const handleBack = () => {
  uni.navigateBack()
}

const handleWorkerConfirm = ({ value }) => {
  console.log('选择的作业员:', value)
}

const getUserListData = async (roleName: string) => {
  const res = await getUerRoleName(roleName)
  console.log('res', res)
  workerOptions.value = res.data
}

const handleAdd = async () => {
  // 新增排班
  console.log('新增排班')
  if (loading.value) return
  loading.value = true
  uni.showLoading({
    title: '正在提交...',
    mask: true,
  })
  try {
    // 调用接口
    const res = await checkSchedule({ machineTaskId: schMachine.value.id })
    uni.hideLoading()
    loading.value = true
    if (res.data) {
      uni.showModal({
        title: '提示',
        content: '该机台已存在加工任务，请确认是否继续？',
        showCancel: false, // false为弹出提示框没有取消键
        cancelText: '取消', // 取消按钮的文字
        confirmText: '继续', // 确认按钮文字
        success: function (res) {
          if (res.confirm) {
            // 点击确认时，执行
            handleAddSubmit()
          }
        },
      })
    } else {
      // 执行新增逻辑
      handleAddSubmit()
    }
    console.log('res', res)
  } catch (error) {
  } finally {
    loading.value = false
    uni.hideLoading()
  }
}

const handleModify = () => {
  // 修改逻辑
  console.log('修改排班')
  if (loading.value) return
  const data = {
    id: schMachine.value.scheduleId,
    machineTaskId: schMachine.value.id,
    personType: workType.value,
    userId: undefined,
    workName: undefined,
  }
  if (workType.value === 0) {
    // 作业员
    if (!selectedWorker.value) {
      toast.error({
        msg: '请选择作业员',
        duration: 2000,
      })
      return
    }
    data.userId = selectedWorker.value
  } else if (workType.value === 1) {
    // 临时工
    if (!selectedWorker.value) {
      toast.error({
        msg: '请选择临时工',
        duration: 2000,
      })
      return
    }
    data.userId = selectedWorker.value
  }
  loading.value = true
  uni.showLoading({
    title: '提交中',
    mask: true,
  })
  editSchedule(data)
    .then((res) => {
      uni.hideLoading()
      loading.value = false
      if (res.data) {
        uni.showToast({
          title: '提交成功',
          icon: 'success',
          duration: 2000,
        })
        setTimeout(function () {
          uni.navigateTo({
            url: '/pages/arrangement/tasks',
          })
        }, 2000)
      }
    })
    .catch((err) => {
      uni.hideLoading()
      loading.value = false
      console.log(err)
    })
}

const handleException = () => {
  // 异常标记逻辑
  console.log('异常标记')
  message
    .prompt({
      title: '设备异常标记备注',
      inputValue: '',
    })
    .then(async (resp) => {
      console.log('resp', resp)
      console.log('remark', remark.value)
      if (resp.value) {
        if (loading.value) return
        loading.value = true
        uni.showLoading({
          title: '提交中',
          mask: true,
        })
        remark.value = resp.value
        console.log('remark', remark.value)
        await abnormal({
          id: schMachine.value.id,
          remark: remark.value,
        })
          .then((res) => {
            uni.hideLoading()
            loading.value = false
            if (res.data) {
              uni.showToast({
                title: '提交成功',
                icon: 'success',
                duration: 2000,
              })
              setTimeout(function () {
                uni.navigateTo({
                  url: '/pages/arrangement/tasks',
                })
              }, 2000)
            }
          })
          .catch((err) => {
            console.log(err)
            uni.hideLoading()
            loading.value = false
          })
      }
    })
    .catch((error) => {
      console.log(error)
    })
}

const handleWorkTypeConfirm = ({ value }) => {
  selectedWorker.value = ''
  if (value === 0) {
    getUserListData('操作工')
  } else if (value === 1) {
    getUserListData('临时工')
  }
}

const handleAddSubmit = () => {
  // 提交逻辑
  console.log('提交')
  loading.value = true
  uni.showLoading({
    title: '提交中',
    mask: true,
  })
  const data = {
    machineTaskId: schMachine.value.id,
    personType: workType.value,
    userId: undefined,
    workName: undefined,
  }
  data.userId = selectedWorker.value
  addSchedule(data)
    .then((res) => {
      uni.hideLoading()
      loading.value = false
      if (res.data) {
        uni.showToast({
          title: '提交成功',
          icon: 'success',
          duration: 2000,
        })
        setTimeout(function () {
          uni.navigateTo({
            url: '/pages/arrangement/tasks',
          })
        }, 2000)
      }
    })
    .catch((err) => {
      uni.hideLoading()
      loading.value = false
      console.log(err)
    })
}
const cancelAbnormalTask = async () => {
  // 取消异常任务逻辑
  console.log('取消异常标记')
  if (loading.value) return
  loading.value = true
  uni.showLoading({
    title: '提交中',
    mask: true,
  })
  await unabnormal({
    id: schMachine.value.id,
  })
    .then((res) => {
      uni.hideLoading()
      loading.value = false
      if (res.data) {
        uni.showToast({
          title: '提交成功',
          icon: 'success',
          duration: 2000,
        })
        setTimeout(function () {
          uni.navigateTo({
            url: '/pages/arrangement/tasks',
          })
        }, 2000)
      }
    })
    .catch((err) => {
      console.log(err)
      uni.hideLoading()
      loading.value = false
    })
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  padding-bottom: 120rpx;
  background-color: #f5f5f5;
}

.info-card,
.work-order-card,
.schedule-card {
  margin: 24rpx;
}

.info-content {
  padding: 0;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 0 16rpx 0;
}

.section-title {
  position: relative;
  padding-left: 24rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.section-title::before {
  position: absolute;
  top: 50%;
  left: 0;
  width: 6rpx;
  height: 32rpx;
  content: '';
  background-color: #4080ff;
  border-radius: 3rpx;
  transform: translateY(-50%);
}

.work-order-content,
.form-content {
  padding: 0;
}

.button-group {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  gap: 24rpx;
  padding: 32rpx 24rpx;
  background: #fff;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.button-group .wd-button {
  flex: 1;
}
</style>
