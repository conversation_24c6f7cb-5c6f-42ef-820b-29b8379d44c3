export interface IIncomingRes {
  content: IIncomingItem[]
  totalElements: number
}
export interface IIncomingItem {
  materialName: string
  quantity: number
  receivedAt: string
  code: string
  supplierName: string
  inspector: string
  rejectQuantity: number
  remark: string
  traceCode: string
}

export interface IIncomingResource {
  materialId: number
  quantity: number
  batchNo: string
  supplier: string
  inspector: string
  rejectQuantity: number
  remark: string
  forRemark?: string // 取证查询图片路径，多张图片用逗号分隔
}
