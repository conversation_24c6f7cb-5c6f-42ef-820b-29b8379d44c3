// 获取文件地址 - 专为H5环境优化
export function getObjectURL(file: File): string | null {
  // #ifdef H5
  try {
    return window.URL?.createObjectURL(file) || window.webkitURL?.createObjectURL(file) || null
  } catch (e) {
    console.error('H5环境创建对象URL失败:', e)
    return null
  }
  // #endif
}

// 节流函数
export function throttle<T extends (...args: any[]) => any>(
  fn: T,
  wait = 1000,
): (...args: Parameters<T>) => void {
  let timer: ReturnType<typeof setTimeout> | null = null

  return function (this: ThisParameterType<T>, ...args: Parameters<T>) {
    if (!timer) {
      fn.apply(this, args)
      timer = setTimeout(() => {
        timer = null
      }, wait)
    }
  }
}

// 防抖函数
export function debounce<T extends (...args: any[]) => any>(
  fn: T,
  wait = 1000,
): (...args: Parameters<T>) => void {
  let timer: ReturnType<typeof setTimeout> | null = null

  return function (this: ThisParameterType<T>, ...args: Parameters<T>) {
    if (timer) {
      clearTimeout(timer)
    }
    timer = setTimeout(() => {
      fn.apply(this, args)
      timer = null
    }, wait)
  }
}

export const getFileProxyPrefix = () => {
  return import.meta.env.VITE_FILE_PROXY_PREFIX
}
