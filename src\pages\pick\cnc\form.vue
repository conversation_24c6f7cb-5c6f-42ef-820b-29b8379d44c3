<route>
{
  "name": "Cnc<PERSON>ick",
  "style": {
    "navigationBarTitleText": "CNC领料单"
  }
}
</route>

<template>
  <view class="page-container">
    <!-- CNC领料信息表单 -->
    <view class="form-section">
      <view class="section-title">
        <view class="title-bar"></view>
        <text class="title-text">CNC领料信息</text>
      </view>

      <wd-cell-group title="领料人员">
        <!-- 从本地存储获取登录人信息，系统自动填入且不可修改 -->
        <wd-input v-model="userName" placeholder="系统带出名称" readonly />
      </wd-cell-group>

      <wd-cell-group title="批次码">
        <!-- 批次码扫描输入框，支持手动输入和扫码输入 -->
        <view class="input-with-scan">
          <wd-input v-model="batchNo" placeholder="请扫描批次码" class="scan-input" />
          <view class="scan-btn" @tap="scanBatchCode">
            <wd-icon name="scan" class="scan-icon" />
          </view>
        </view>
      </wd-cell-group>

      <wd-cell-group title="原料名称">
        <!-- 使用RemoteSelectWot组件，实现原料名称的远程搜索选择 -->
        <RemoteSelectWot
          v-model="materialId"
          placeholder="请选择原料名称"
          :remote-method="getMaterialPageFn"
          :props="{ label: 'materialName', value: 'id' }"
          @change="handleMaterialChange"
        />
      </wd-cell-group>

      <wd-cell-group title="材料数量">
        <wd-input v-model="pickNum" type="number" placeholder="请输入材料数量" />
      </wd-cell-group>
    </view>

    <!-- 底部提交按钮 -->
    <view class="action-section">
      <wd-button type="primary" block @click="submitForm">确认领料</wd-button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { postPickMaterial } from '@/service/pick'
import { useToast } from 'wot-design-uni'
import { getMaterialPageApi } from '@/service/material'
import RemoteSelectWot from '@/components/common/RemoteSelectWot.vue'
import { useUserStore } from '@/store/user'

// 路由实例
const router = useRouter()
const toast = useToast()

// 从本地存储获取登录人信息
const userStore = useUserStore()
const userName = ref(userStore.userInfo.user.username)

// 表单数据
const batchNo = ref('')
const materialId = ref()
const pickNum = ref()

function getMaterialPageFn(params) {
  return getMaterialPageApi(params)
}
function handleMaterialChange(item) {
  console.log('选择的数据', item)
  materialId.value = item.id
}

// 扫描批次码
const scanBatchCode = async () => {
  try {
    const result = await uni.scanCode({
      scanType: ['qrCode', 'barCode'],
    })
    batchNo.value = result.result
  } catch (error) {
    uni.showToast({
      title: '扫码失败',
      icon: 'none',
    })
  }
}

// 提交表单
const submitForm = async () => {
  if (!batchNo.value) {
    uni.showToast({
      title: '请扫描批次码',
      icon: 'none',
    })
    return
  }
  if (!materialId.value) {
    uni.showToast({
      title: '请输入原料名称',
      icon: 'none',
    })
    return
  }
  if (!pickNum.value) {
    uni.showToast({
      title: '请输入材料数量',
      icon: 'none',
    })
    return
  }

  const resp = await postPickMaterial({
    batchNo: batchNo.value,
    materialId: materialId.value,
    pickNum: pickNum.value,
    pickerId: userStore.userInfo.user.id,
    category: 'cnc',
  })

  toast.success({
    msg: '保存成功',
  })

  // 保存成功后跳转
  setTimeout(() => {
    uni.navigateTo({
      url: '/pages/pick/cnc/index',
    })
  }, 1500)
}
</script>

<style lang="scss" scoped>
/**
 * CNC领料表单页面样式
 * 使用统一的设计系统样式
 * 更新时间: 2025-06-18
 */

// 引入设计系统变量
@import '@/style/design-system.scss';

// 页面容器、表单区域、区块标题、扫描输入框、底部操作区域
// 均使用标准样式类，已在 components.scss 中定义，无需重复定义

// Pick模块特有的样式调整
.form-section {
  // 为Pick模块添加左边框标识，使用主色调
  border-left: 4rpx solid $primary-color;
}

// 扫描图标颜色调整为主色调
.scan-icon {
  color: $primary-color !important;
}
</style>
