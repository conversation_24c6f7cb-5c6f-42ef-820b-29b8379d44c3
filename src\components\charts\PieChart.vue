<template>
  <view class="pie-chart" :style="{ width: pieWidth, height: pieHeight }">
    <l-echart ref="chart" :ec="option" :customStyle="customStyle" :id="chartId" />
  </view>
</template>

<script>
import { defineComponent, ref, computed, watch, onMounted, nextTick, getCurrentInstance } from 'vue'
</script>

<script setup>
// 引入echarts核心模块
import * as echarts from 'echarts/core'
// 引入饼图图表
import { PieChart as EChartsPie } from 'echarts/charts'
// 引入提示框和标题组件
import { TitleComponent, TooltipComponent, LegendComponent } from 'echarts/components'

// 注册必须的组件
echarts.use([TitleComponent, TooltipComponent, LegendComponent, EChartsPie])

// 全局重试计数器和已初始化的图表追踪
if (!globalThis.__chartRetries) {
  globalThis.__chartRetries = {}
}
if (!globalThis.__initializedCharts) {
  globalThis.__initializedCharts = {}
}

// 组件属性
const props = defineProps({
  // 饼图宽度
  pieWidth: {
    type: String,
    default: '100%',
  },
  // 饼图高度
  pieHeight: {
    type: String,
    default: '100%',
  },
  // 中心标题
  titlePie: {
    type: String,
    default: '',
  },
  // 中心标题大小
  titlePieSize: {
    type: Number,
    default: 12,
  },
  // 中心百分比数字
  titleNum: {
    type: String,
    default: '0%',
  },
  // 中心百分比大小
  titleNumSize: {
    type: Number,
    default: 12,
  },
  // 中心标题颜色
  titleColor: {
    type: String,
    default: '#333333',
  },
  // 饼图数据
  dataPie: {
    type: Array,
    default: () => [],
  },
  // 颜色配置
  colorData: {
    type: Array,
    default: () => ['#36B876', '#F99E38', '#B5B5B5'],
  },
  // 自定义样式
  customStyle: {
    type: String,
    default: '',
  },
  // 图表实例索引
  chartIndex: {
    type: [Number, String],
    default: null,
  },
})

// 图表实例引用
const chart = ref(null)

// 组件是否已挂载
const isMounted = ref(false)

// 为每个图表实例生成唯一ID
const instance = getCurrentInstance()
const instanceId = instance.uid || Math.floor(Math.random() * 1000000)
const randomId = Math.floor(Math.random() * 1000000)
const chartId = computed(() => {
  if (props.chartIndex !== null) {
    return `pie-chart-${props.chartIndex}-${randomId}`
  }
  return `pie-chart-${instanceId}`
})

// 延迟执行函数
const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms))

// 计算属性：图表配置
const option = computed(() => {
  return {
    option: {
      color: props.colorData,
      animation: false, // 禁用动画，提高性能
      series: [
        {
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
          emphasis: {
            scale: false,
          },
          data: props.dataPie,
          hoverAnimation: false,
        },
      ],
      graphic: {
        elements: [
          {
            type: 'text',
            left: 'center',
            top: '45%',
            style: {
              text: props.titlePie,
              fontSize: props.titlePieSize,
              fill: props.titleColor,
            },
          },
          {
            type: 'text',
            left: 'center',
            top: '55%',
            style: {
              text: props.titleNum,
              fontSize: props.titleNumSize,
              fill: props.titleColor,
            },
          },
        ],
      },
    },
  }
})

// 监听饼图数据变化，更新图表
watch(
  () => props.dataPie,
  () => {
    if (isMounted.value && chart.value) {
      updateChart()
    }
  },
  { deep: true },
)

// 监听标题或百分比变化，更新图表
watch([() => props.titlePie, () => props.titleNum, () => props.titleColor], () => {
  if (isMounted.value && chart.value) {
    updateChart()
  }
})

// 检查DOM元素是否准备好
const checkDOMReady = async () => {
  try {
    return await new Promise((resolve) => {
      uni
        .createSelectorQuery()
        .in(instance)
        .select(`#${chartId.value}`)
        .boundingClientRect((data) => {
          if (data && data.width && data.height && data.width > 0 && data.height > 0) {
            resolve(true)
          } else {
            resolve(false)
          }
        })
        .exec()
    })
  } catch (e) {
    console.error('DOM检测异常:', e)
    return false
  }
}

// 初始化图表
const initChart = async () => {
  // 防止重复初始化
  const id = chartId.value
  if (globalThis.__initializedCharts[id]) {
    return
  }

  // 延迟以确保DOM准备好
  await nextTick()
  await delay(500)

  // 检查DOM是否已准备好
  const isDOMReady = await checkDOMReady()

  if (!isDOMReady) {
    console.warn(`图表DOM #${id} 未准备好，稍后重试`)
    setTimeout(() => retryInit(), 800)
    return
  }

  try {
    if (!chart.value) {
      console.warn(`图表组件 #${id} 的ref还未准备好`)
      setTimeout(() => retryInit(), 500)
      return
    }

    // 使用回调函数接收初始化结果
    await chart.value.init((res) => {
      if (res && res.chart) {
        console.log(`图表 #${id} 初始化回调成功`)
        globalThis.__initializedCharts[id] = true
        isMounted.value = true
        updateChart()
      }
    })

    // 防止没有接收到回调的情况
    setTimeout(() => {
      if (!isMounted.value) {
        isMounted.value = true
        globalThis.__initializedCharts[id] = true
        updateChart()
      }
    }, 500)
  } catch (err) {
    console.error(`图表 #${id} 初始化失败:`, err)
    setTimeout(() => retryInit(), 1000)
  }
}

// 重试初始化
const retryInit = async () => {
  const id = chartId.value

  try {
    // 再次检查DOM
    const isDOMReady = await checkDOMReady()

    if (!isDOMReady) {
      throw new Error(`DOM元素 #${id} 尚未准备好`)
    }

    if (!isMounted.value && chart.value) {
      console.log(`重试初始化图表 #${id}`)

      // 尝试先清理旧实例
      try {
        chart.value.dispose()
        await delay(200)
      } catch (e) {
        // 忽略清理错误
      }

      await chart.value.init()
      isMounted.value = true
      globalThis.__initializedCharts[id] = true
      updateChart()
      console.log(`图表 #${id} 重试初始化成功`)
    }
  } catch (e) {
    console.error(`图表 #${id} 重试初始化失败:`, e)

    // 跟踪重试次数
    if (!globalThis.__chartRetries[id]) {
      globalThis.__chartRetries[id] = 0
    }

    if (globalThis.__chartRetries[id] < 5) {
      // 增加最大重试次数
      globalThis.__chartRetries[id]++
      const retryDelay = 800 + globalThis.__chartRetries[id] * 500
      console.log(`将在${retryDelay}ms后进行第${globalThis.__chartRetries[id]}次重试`)
      setTimeout(() => retryInit(), retryDelay)
    } else {
      console.warn(`图表 #${id} 初始化失败，已达到最大重试次数`)
      // 尝试强制初始化
      isMounted.value = true
      globalThis.__initializedCharts[id] = true
      try {
        if (chart.value) {
          updateChart()
        }
      } catch (err) {
        console.error('强制更新图表失败:', err)
      }
    }
  }
}

// 更新图表
const updateChart = () => {
  if (!chart.value) return

  try {
    chart.value.setOption(option.value.option)
  } catch (err) {
    console.error('更新图表失败:', err)
  }
}

// 组件挂载后初始化
onMounted(async () => {
  // 给DOM足够的时间准备
  await delay(800)
  initChart()
})
</script>

<style scoped>
.pie-chart {
  position: relative;
  box-sizing: border-box;
  width: v-bind(pieWidth);
  height: v-bind(pieHeight);
}
</style>
