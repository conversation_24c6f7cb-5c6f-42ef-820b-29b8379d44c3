import { httpUpload } from '@/utils/http'

/**
 * 文件上传接口类型定义
 */
export interface IUploadResponse {
  url: string
  [key: string]: any
}

/**
 * 文件上传选项
 */
export interface IUploadOptions {
  /** 上传接口地址 */
  url: string
  /** 文件路径 */
  filePath: string
  /** 文件字段名，默认为 'file' */
  name?: string
  /** 额外的表单数据 */
  formData?: Record<string, any>
  /** 自定义请求头 */
  header?: Record<string, any>
}

/**
 * 统一的文件上传服务
 * 基于 http.ts 的 httpUpload 方法，统一认证和错误处理
 */
export const uploadFile = async (options: IUploadOptions): Promise<IUploadResponse> => {
  try {
    const response = await httpUpload<IUploadResponse>(
      options.url,
      options.filePath,
      options.name || 'file',
      options.formData,
      options.header,
    )

    // 适配不同的响应格式，确保返回包含 url 字段
    return {
      url: response?.url || response?.data?.url || '',
      ...response,
    }
  } catch (error) {
    console.error('文件上传失败:', error)
    throw error
  }
}