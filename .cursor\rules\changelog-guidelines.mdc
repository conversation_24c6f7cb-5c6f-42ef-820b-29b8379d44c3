---
description: 
globs: 
alwaysApply: true
---
# 变更日志记录指南

## 变更记录要求

每次修改代码后，必须在项目根目录的`changeLog`文件夹中创建或更新当天的变更日志文件。变更日志有助于跟踪项目的历史变化，便于团队成员了解代码演进过程。

## 文件命名规则

变更日志文件必须按照日期命名，格式为：`changeLog年月日.md`，例如：`changeLog20240528.md`。

每天应该创建一个新的变更日志文件，文件放在changeLog这个文件夹中，所有当天的修改都记录在这个文件中。

## 记录格式

变更记录应遵循以下Markdown格式：

```markdown
# 变更日志 - 年月日

## [HH:mm:ss] [变更类型]: 简短描述

**作者**: 你的名字  
**文件**: 被修改的文件路径

### 变更描述
详细说明变更的原因和目的

### 变更详情
**原代码**:
```代码语言
// 这里是修改前的原始代码
```

**新代码**:
```代码语言
// 这里是修改后的新代码
```
```

## 使用时间

当需要在变更日志中记录具体时间时，必须使用`mcp_time-mcp_current_time`MCP工具获取准确的当前时间。如果该工具调用失败，直接在终端获取当前时间，我的是Windows电脑。

```javascript
// 示例代码：获取当前时间（中国时区）
const currentTime = callFunction('mcp_time-mcp_current_time', { 
  format: 'YYYY-MM-DD HH:mm:ss',
  timezone: 'Asia/Shanghai'
});
```

> **注意**：所有时间记录必须使用中国时区（Asia/Shanghai），确保团队成员看到一致的时间戳。

## 变更类型

变更类型应与Git提交类型保持一致：

- `feat` - 新功能
- `fix` - 修复Bug
- `refactor` - 代码重构
- `style` - 代码风格修改
- `docs` - 文档更新
- `perf` - 性能优化
- `test` - 测试相关
- `chore` - 其他修改

## 变更记录示例

```markdown
# 变更日志 - 2024年5月28日

## [10:30:45] feat: 添加用户登录表单验证功能

**作者**: 张三  
**文件**: src/pages/login/login.vue

### 变更描述
添加表单验证功能，防止用户提交空白或格式不正确的登录信息

### 变更详情
**原代码**:
```javascript
<script setup lang="ts">
const formData = reactive({
  username: '',
  password: ''
})

const handleLogin = () => {
  loginService.login(formData)
}
</script>
```

**新代码**:
```javascript
<script setup lang="ts">
const formData = reactive({
  username: '',
  password: ''
})

const formRules = {
  username: [{ required: true, message: '用户名不能为空' }],
  password: [
    { required: true, message: '密码不能为空' },
    { min: 6, message: '密码长度不能少于6位' }
  ]
}

const handleLogin = () => {
  formRef.value.validate().then(valid => {
    if (valid) {
      loginService.login(formData)
    }
  })
}
</script>
```
```

## 注意事项

1. 变更记录应使用Markdown格式以增强可读性和结构化
2. 原代码和新代码应包含足够的上下文，确保变更易于理解
3. 如果修改涉及多个文件，应分别列出每个文件的变更
4. 对于大型重构，可以适当精简代码示例，但必须保留关键变更
5. 每天开始工作时检查是否需要创建新的日期命名的变更日志文件
6. 代码块应指定适当的语言（如javascript, typescript, vue等）以获得语法高亮
