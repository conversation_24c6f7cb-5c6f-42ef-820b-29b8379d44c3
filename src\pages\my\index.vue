<route lang="json5" type="page">
{
  layout: 'tabbar',
  needLogin: true,
  style: {
    navigationBarTitleText: '我的',
    backgroundColor: '#f3f4f6',
  },
}
</route>

<template>
  <view class="page-container">
    <!-- 用户信息卡片 -->
    <view class="user-profile-card">
      <view class="flex items-center">
        <!-- 使用昵称首字母作为头像 -->
        <view class="avatar-container">
          <text class="avatar-text">{{ getAvatarText() }}</text>
        </view>
        <view class="ml-4 flex-1">
          <view class="text-lg font-bold">{{ userStore.userInfo?.user.nickName }}</view>
          <view class="text-sm text-gray-500 mt-1">
            用户名: {{ userStore.userInfo?.user.username }}
          </view>
        </view>
      </view>
    </view>

    <!-- 用户详细信息 -->
    <view class="mt-4">
      <wd-cell-group border>
        <wd-cell title="手机号码" :value="userStore.userInfo?.user.phone" />
        <wd-cell title="所属部门" :value="userStore.userInfo?.user.dept.name" />
      </wd-cell-group>

      <!-- 自定义角色展示 -->
      <view class="bg-white p-4 mt-2 flex items-start">
        <view class="text-gray-600 min-w-20">我的角色</view>
        <view class="flex-1 flex flex-wrap gap-2">
          <wd-tag
            v-for="role in userStore.userInfo?.user?.roles"
            :key="role.id"
            type="primary"
            plain
            round
          >
            {{ role.name }}
          </wd-tag>
        </view>
      </view>
    </view>

    <!-- 功能菜单卡片 -->
    <view class="mt-4">
      <wd-cell-group border>
        <wd-cell title="我的任务" is-link @click="handleMenuClick('task')">
          <template #icon>
            <view class="menu-icon task-icon">
              <wd-icon name="clock" size="18px" color="#ffffff" />
            </view>
          </template>
        </wd-cell>

        <wd-cell title="关于我们" is-link @click="handleMenuClick('about')">
          <template #icon>
            <view class="menu-icon about-icon">
              <wd-icon name="user-avatar" size="18px" color="#ffffff"></wd-icon>
            </view>
          </template>
        </wd-cell>
      </wd-cell-group>
    </view>

    <view class="px-4 mt-8">
      <wd-button type="primary" block @click="handleLogout">退出登录</wd-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store'

const userStore = useUserStore()

// 获取头像文字（昵称首字母）
const getAvatarText = () => {
  const nickName = userStore.userInfo?.user.nickName
  return nickName ? nickName.charAt(0).toUpperCase() : '用'
}

// 处理菜单点击事件
const handleMenuClick = (type: string) => {
  switch (type) {
    case 'task':
      // 跳转到我的任务页面
      uni.navigateTo({
        url: '/pages/my/myTask/index',
      })
      break
    case 'security':
      uni.showToast({
        title: '安全中心功能开发中',
        icon: 'none',
      })
      // TODO: 跳转到安全中心页面
      // uni.navigateTo({
      //   url: '/pages/security/index'
      // })
      break
    case 'about':
      // 跳转到关于我们页面
      uni.navigateTo({
        url: '/pages/about-us/index',
      })
      break
    default:
      break
  }
}

// 退出登录处理
const handleLogout = () => {
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        userStore.clearUserInfo()
        uni.reLaunch({
          url: '/pages/login/login',
        })
      }
    },
  })
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  padding: 16px;
  background-color: #f3f4f6;
}

.user-profile-card {
  padding: 16px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 头像容器样式 */
.avatar-container {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #44a1db 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.avatar-text {
  color: #ffffff;
  font-size: 28px;
  font-weight: bold;
  text-align: center;
}

/* 功能菜单图标样式 */
.menu-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;

  &.task-icon {
    background: linear-gradient(135deg, #0a7efd 0%, #63c7ee 100%);
  }

  &.about-icon {
    background: linear-gradient(135deg, #0a7efd 0%, #63c7ee 100%);
  }
}
</style>
