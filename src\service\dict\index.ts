import { http } from '@/utils/http'

// 字典接口类型定义
export interface IDictDetail {
  id: string
  label: string
  value: string
}

export interface IDictItem {
  dictDetails: IDictDetail[]
}

export interface IDictResponse {
  content: IDictItem[]
}

/**
 * 根据字典名称获取字典数据
 * @param params 查询参数
 * @returns 字典数据
 */
export const getNametFindDict = (params: { blurry: string }) => {
  return http.get<IDictResponse>('/api/dict', params)
}

export default { getNametFindDict }
