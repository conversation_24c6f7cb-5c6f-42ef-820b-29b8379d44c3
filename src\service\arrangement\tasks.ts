import { http } from '@/utils/http'
import { IMachineOrder, IMachineOrderRes, IScheduleListRes } from './types'

export const getMachineOrderList = (data: IMachineOrder) => {
  return http.get<IMachineOrderRes[]>('/api/v1/terminal/schedule/getMachineOrderList', data)
}

export const getScheduleList = (machineOrderId: number) => {
  return http.get<IScheduleListRes>('/api/v1/terminal/schedule/getScheduleList', {
    machineOrderId,
  })
}

export const complete = (machineOrderId: number) => {
  return http.post('/api/v1/terminal/schedule/complete', {
    machineOrderId,
  })
}
