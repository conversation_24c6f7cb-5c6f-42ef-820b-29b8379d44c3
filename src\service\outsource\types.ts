export interface IOutsourceRes {
  content: IOutsourceItem[]
  totalElements: number
}

export interface IOutsourceItem {
  productName: string
  supplier: string
  erpCode: string
  processName: string
  createTime: string
  inspector: string
  quantity: number
}

export interface IOutsourceResource {
  quantity: number
  productName: string
  supplier: string
  erpCode: string
  processName: string
  inspector: string
}
