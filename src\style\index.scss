/**
 * S-MES移动端主样式文件
 * 统一引入设计系统和组件样式
 * 更新时间: 2025-06-18
 */

// 引入字体图标
@import './iconfont.css';

// 引入设计系统
@import './design-system.scss';

// 引入组件样式
@import './components.scss';

// 引入布局样式
@import './layouts.scss';

// 引入工具类样式
@import './utilities.scss';

// 全局基础样式重置
* {
  box-sizing: border-box;
}

page {
  background-color: $bg-page;
  color: $text-primary;
  font-size: $font-size-base;
  line-height: $line-height-base;
}

// 全局滚动条样式（H5端）
/* #ifdef H5 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: $bg-input;
  border-radius: $border-radius-sm;
}

::-webkit-scrollbar-thumb {
  background: $border-base;
  border-radius: $border-radius-sm;

  &:hover {
    background: $border-dark;
  }
}
/* #endif */

// 兼容旧样式的测试类（保留以防有地方在使用）
.test {
  // 可以通过 @apply 多个样式封装整体样式
  @apply mt-4 ml-4;

  padding-top: 4px;
  color: red;
}
