<template>
  <l-echart ref="chart" :customStyle="customStyle" v-bind="$attrs"></l-echart>
</template>

<script>
import LEchart from '../l-echart/l-echart.vue'

export default {
  name: 'lime-echart',
  components: {
    LEchart,
  },
  props: {
    customStyle: String,
  },
  methods: {
    init(callback) {
      return this.$refs.chart.init(callback)
    },
    setOption() {
      this.$refs.chart.setOption(...arguments)
    },
    setChart() {
      this.$refs.chart.setChart(...arguments)
    },
    showLoading() {
      this.$refs.chart.showLoading()
    },
    hideLoading() {
      this.$refs.chart.hideLoading()
    },
    clear() {
      this.$refs.chart.clear()
    },
    dispose() {
      this.$refs.chart.dispose()
    },
  },
}
</script>

<style scoped></style>
