# S-MES移动端样式风格统一指南

## 概述

本文档定义了S-MES移动端项目的统一样式规范，旨在确保整个项目的视觉一致性和代码可维护性。

## 设计系统

### 颜色系统

#### 主色调
- **主色调**: `#2979ff` - 用于主要按钮、链接和强调元素
- **主色调浅色**: `#5a9cff` - 用于悬停状态
- **主色调深色**: `#1565c0` - 用于激活状态

#### 功能色
- **成功色**: `#19be6b` - 用于成功状态、合格标识
- **警告色**: `#ff9900` - 用于警告状态、待处理标识
- **错误色**: `#fa3534` - 用于错误状态、不合格标识
- **信息色**: `#909399` - 用于一般信息提示

#### 文本颜色
- **主要文本**: `#333333` - 用于标题、重要内容
- **次要文本**: `#666666` - 用于描述性文本
- **辅助文本**: `#999999` - 用于提示信息
- **占位符文本**: `#c0c4cc` - 用于输入框占位符
- **反色文本**: `#ffffff` - 用于深色背景上的文本

#### 背景颜色
- **页面背景**: `#f5f5f5` - 应用主背景色
- **卡片背景**: `#ffffff` - 内容卡片背景
- **输入框背景**: `#f8f8f8` - 表单输入框背景
- **悬停背景**: `#f1f1f1` - 交互元素悬停背景

### 间距系统

基于8rpx的间距系统，确保视觉节奏的一致性：

- **极小间距**: `8rpx` - 用于紧密相关的元素
- **小间距**: `16rpx` - 用于相关元素之间
- **中等间距**: `24rpx` - 用于组件内部间距
- **大间距**: `32rpx` - 用于页面边距、区块间距
- **超大间距**: `40rpx` - 用于重要区块分隔
- **极大间距**: `48rpx` - 用于页面级别的大间距

### 字体系统

#### 字体大小
- **极小字体**: `20rpx` - 用于辅助信息
- **小字体**: `24rpx` - 用于次要内容
- **基础字体**: `28rpx` - 用于正文内容
- **大字体**: `32rpx` - 用于小标题
- **超大字体**: `36rpx` - 用于主标题
- **极大字体**: `40rpx` - 用于页面标题

#### 字体粗细
- **细体**: `300` - 用于辅助信息
- **正常**: `400` - 用于正文内容
- **中等**: `500` - 用于小标题
- **粗体**: `600` - 用于重要标题

### 圆角系统

- **极小圆角**: `2rpx` - 用于边框装饰
- **小圆角**: `4rpx` - 用于小元素
- **基础圆角**: `8rpx` - 用于按钮、卡片
- **大圆角**: `12rpx` - 用于大卡片
- **超大圆角**: `16rpx` - 用于特殊元素
- **圆形**: `50%` - 用于头像、图标

## 组件规范

### 页面布局

#### 标准页面结构
```vue
<template>
  <view class="page-container">
    <!-- 页面内容 -->
    <view class="content-section">
      <!-- 具体内容 -->
    </view>
    
    <!-- 底部操作区域（如需要） -->
    <view class="action-section">
      <wd-button type="primary" block>确认</wd-button>
    </view>
  </view>
</template>
```

#### 表单页面结构
```vue
<template>
  <view class="page-container">
    <view class="form-section">
      <view class="section-title">
        <view class="title-bar"></view>
        <text class="title-text">表单标题</text>
      </view>
      
      <wd-cell-group title="字段标签">
        <wd-input v-model="value" placeholder="请输入内容" />
      </wd-cell-group>
    </view>
    
    <view class="action-section">
      <wd-button type="primary" block>提交</wd-button>
    </view>
  </view>
</template>
```

### 表单组件

#### 输入框规范
- 统一使用 `wd-input` 组件
- 占位符文本使用友好的提示语
- 必填字段在标签后添加红色星号

#### 选择器规范
- 物料选择统一使用 `RemoteSelectWot` 组件
- 下拉选择使用 `wd-select` 组件
- 日期选择使用 `wd-datetime-picker` 组件

#### 扫描功能规范
```vue
<view class="input-with-scan">
  <wd-input v-model="batchNo" placeholder="请扫描批次码" class="scan-input" />
  <view class="scan-btn" @tap="scanBatchNo">
    <wd-icon name="scan" class="scan-icon" />
  </view>
</view>
```

### 按钮规范

#### 主要操作按钮
- 使用 `type="primary"` 的蓝色按钮
- 页面底部固定位置使用 `block` 属性
- 按钮文字简洁明确

#### 次要操作按钮
- 使用 `type="default"` 的默认按钮
- 可与主要按钮组合使用

### 卡片组件

#### 信息展示卡片
```vue
<view class="data-card">
  <view class="card-header">
    <text class="card-title">卡片标题</text>
    <view class="card-status status-success">状态标签</view>
  </view>
  <view class="card-content">
    <view class="data-row">
      <text class="data-label">字段名称</text>
      <text class="data-value">字段值</text>
    </view>
  </view>
</view>
```

#### 状态标签规范
- 成功状态：绿色背景 `status-success`
- 警告状态：橙色背景 `status-warning`
- 错误状态：红色背景 `status-error`
- 信息状态：灰色背景 `status-info`

## 质量检验模块专用规范

### 颜色规范
- **合格状态**: 使用成功色 `#19be6b`
- **不合格状态**: 使用错误色 `#fa3534`
- **待检验状态**: 使用警告色 `#ff9900`
- **模块主色**: 使用主色调 `#2979ff`

### 卡片样式
质量检验模块的卡片使用特殊的左边框标识：
```scss
.quality-card {
  border-left: 4rpx solid $quality-primary;
}
```

## CSS类命名规范

### 命名约定
- 使用连字符分隔的小写命名：`form-section`、`data-card`
- 状态类使用前缀：`status-success`、`is-active`
- 工具类使用简短描述：`text-center`、`mb-lg`

### 常用类名
- 容器类：`page-container`、`content-section`、`form-section`
- 组件类：`data-card`、`info-card`、`list-item`
- 状态类：`status-success`、`status-warning`、`status-error`
- 工具类：`text-center`、`mb-lg`、`p-md`

## 最佳实践

### 样式编写原则
1. 优先使用设计系统中定义的变量
2. 避免硬编码颜色值和尺寸
3. 使用语义化的类名
4. 保持样式的可复用性

### 组件使用原则
1. 统一使用wot-design-uni组件库
2. 保持组件使用方式的一致性
3. 遵循组件的设计规范

### 响应式设计
1. 优先考虑移动端体验
2. 使用相对单位（rpx）确保适配性
3. 合理使用条件编译适配不同平台

## 代码审查清单

### 样式检查项
- [ ] 是否使用了设计系统中的颜色变量
- [ ] 是否使用了标准的间距规范
- [ ] 是否遵循了组件使用规范
- [ ] 是否使用了语义化的类名
- [ ] 是否避免了重复的样式代码

### 组件检查项
- [ ] 表单是否使用了标准的布局结构
- [ ] 按钮是否使用了正确的类型和样式
- [ ] 卡片是否使用了统一的样式
- [ ] 状态标签是否使用了正确的颜色

## 更新日志

### 2025-06-18
- 建立完整的设计系统
- 创建组件样式规范
- 定义布局系统和工具类
- 编写样式指南文档
