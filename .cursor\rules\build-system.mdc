---
description: 
globs: 
alwaysApply: true
---
# 构建系统指南

## Vite 构建

项目使用Vite作为构建工具，主要配置文件为[vite.config.ts](mdc:vite.config.ts)。Vite配置了以下主要功能：

- 多平台构建支持（H5、微信小程序、App等）
- 自动导入（unplugin-auto-import）
- 原子化CSS（UnoCSS）
- 自动生成路由（vite-plugin-uni-pages）
- 统一布局系统（vite-plugin-uni-layouts）
- 环境变量管理

## 环境变量

环境变量配置在`env`目录下，按不同环境分为：

- `.env.development` - 开发环境
- `.env.production` - 生产环境

## NPM脚本

主要的NPM脚本定义在[package.json](mdc:package.json)中：

- `dev` / `dev:h5` - 启动H5开发服务器
- `dev:mp-weixin` - 开发微信小程序
- `dev:app` - 开发App
- `build` / `build:h5` - 构建H5生产版本
- `build:mp-weixin` - 构建微信小程序
- `build:app` - 构建App

## 代码质量工具

项目集成了多种代码质量工具：

- ESLint - 代码规范检查
- Prettier - 代码格式化
- StyleLint - CSS规范检查
- husky - Git钩子
- lint-staged - 对暂存区文件进行检查

Git提交前会自动运行lint-staged进行代码检查和格式化。

