---
description: 
globs: 
alwaysApply: true
---
# 代码规范指南
重要：必须使用中文回答我
## 风格指南

项目使用严格的代码风格检查，包括：

- **ESLint** - JavaScript/TypeScript代码风格检查，配置文件为 [.eslintrc.cjs](mdc:.eslintrc.cjs)
- **Prettier** - 代码格式化工具，配置文件为 [.prettierrc.cjs](mdc:.prettierrc.cjs)
- **StyleLint** - CSS/SCSS代码风格检查，配置文件为 [.stylelintrc.cjs](mdc:.stylelintrc.cjs)
- **EditorConfig** - 跨编辑器代码样式配置，文件为 [.editorconfig](mdc:.editorconfig)
- **must** - 可以使用重复组件的地方必须使用，比如文件/图片上传组件

## 提交规范

项目使用Conventional Commits规范进行Git提交，配置文件为 [.commitlintrc.cjs](mdc:.commitlintrc.cjs)。提交类型包括：

- `feat:` - 新功能
- `fix:` - 修复Bug
- `perf:` - 性能优化
- `style:` - 代码风格调整
- `refactor:` - 重构代码
- `docs:` - 文档修改
- `test:` - 添加测试
- `build:` - 构建配置调整
- `ci:` - CI配置调整
- `chore:` - 其他修改

## 代码自动导入

项目使用unplugin-auto-import插件自动导入Vue和uni-app的API，无需在每个组件中手动导入。相关全局类型定义已在 [.eslintrc-auto-import.json](mdc:.eslintrc-auto-import.json) 中注册。

## TypeScript

项目使用TypeScript开发，tsconfig配置文件为 [tsconfig.json](mdc:tsconfig.json)。

