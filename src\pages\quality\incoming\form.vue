<route lang="json5">
{
  needLogin: true,
  style: {
    navigationBarTitleText: '来料检管理',
  },
}
</route>
<template>
  <view class="page-container">
    <!-- 来料检信息表单 -->
    <view class="form-section">
      <view class="section-title">
        <view class="title-bar"></view>
        <text class="title-text">来料检信息</text>
      </view>

      <wd-cell-group title="检验人员">
        <!-- 从本地存储获取登录人信息，系统自动填入且不可修改 -->
        <wd-input v-model="inspector" placeholder="系统带出名称" readonly />
      </wd-cell-group>

      <wd-cell-group title="来料名称">
        <!-- 使用RemoteSelectWot组件，实现物料名称的远程搜索选择 -->
        <RemoteSelectWot
          v-model="materialId"
          placeholder="请选择来料名称"
          :remote-method="getMaterialPageFn"
          :props="{ label: 'materialName', value: 'id' }"
          @change="handleMaterialChange"
        />
      </wd-cell-group>

      <wd-cell-group title="批次码">
        <!-- 批次码扫描输入框，支持手动输入和扫码输入 -->
        <view class="input-with-scan">
          <wd-input v-model="code" placeholder="请扫描批次码" class="scan-input" />
          <view class="scan-btn" @tap="scanBatchNo">
            <wd-icon name="scan" class="scan-icon" />
          </view>
        </view>
      </wd-cell-group>

      <wd-cell-group title="供应商名称">
        <wd-input v-model="supplierName" placeholder="请输入供应商名称" />
      </wd-cell-group>

      <wd-cell-group title="数量">
        <wd-input v-model="quantity" type="number" placeholder="请输入数量" />
      </wd-cell-group>

      <wd-cell-group title="不良品数量">
        <wd-input v-model="rejectQuantity" type="number" placeholder="请输入不良品数量" />
      </wd-cell-group>

      <wd-cell-group title="取证查询">
        <!-- 图片上传组件，支持多张图片上传，最多6张 -->
        <view class="upload-container">
          <ImageUpload
            action="/api/localStorage/pictures"
            v-model:fileList="imageList"
            :max-count="6"
            :before-upload="handleBeforeUpload"
            @upload-success="onUploadSuccess"
          />
        </view>
      </wd-cell-group>

      <wd-cell-group title="备注">
        <wd-textarea v-model="remark" placeholder="请输入备注" />
      </wd-cell-group>
    </view>

    <!-- 底部提交按钮 -->
    <view class="action-section">
      <wd-button type="primary" block @click="handleSubmit">确认</wd-button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { postIncoming } from '@/service/incoming'
import { IIncomingResource } from '@/service/incoming/types'
import ImageUpload from '@/components/common/ImageUpload.vue'
import { useToast } from 'wot-design-uni'
import { convertImageListToString } from '@/utils/image'
import { useUserStore } from '@/store/user'
import { getMaterialPageApi } from '@/service/material'
import RemoteSelectWot from '@/components/common/RemoteSelectWot.vue'

// 控制工单信息和绑定类型信息的显示

const userStore = useUserStore()

// 检验人员，从用户store中获取当前登录用户的名称
const inspector = ref(userStore.userInfo.user.username)
// 来料数量
const quantity = ref()
// 来料物料ID
const materialId = ref()
// 批次码
const code = ref('')
// 供应商名称
const supplierName = ref('')
// 不良品数量
const rejectQuantity = ref()
// 备注信息
const remark = ref('')

// 图片上传相关变量
const toast = useToast()
const imageList = ref([]) // 图片文件列表，用于ImageUpload组件的v-model绑定

const incomingResource = ref<IIncomingResource>()

// 物料选择相关函数，参考pick/cnc页面的实现方式
function getMaterialPageFn(params) {
  return getMaterialPageApi(params)
}

function handleMaterialChange(item) {
  console.log('选择的物料数据', item)
  materialId.value = item.id
}

// 图片上传前校验函数，参考completed-inspection页面的实现
const handleBeforeUpload = ({ files, resolve }) => {
  // files 是一个数组，包含了本次选择的所有待上传文件
  for (const file of files) {
    // 校验单个文件的大小，限制为2MB
    if (file.size > 4 * 1024 * 1024) {
      // 提示更具体的文件名
      toast.error(`图片 "${file.name}" 大小不能超过 2MB`)

      // 调用 resolve(false) 来阻止本次上传操作
      resolve(false)

      // 一旦有文件不合格，立刻停止后续检查
      return
    }

    // 可以在这里添加其他校验，比如文件类型
    // if (!file.type.startsWith('image/')) {
    //   toast.error(`"${file.name}" 不是一个有效的图片文件`);
    //   resolve(false);
    //   return;
    // }
  }

  // 如果所有文件都通过了校验，调用 resolve(true) 允许上传
  resolve(true)
}

// 图片上传成功回调函数，主要用于业务逻辑处理
const onUploadSuccess = ({ file, fileList }) => {
  console.log('父组件监听到上传成功，最新的文件列表:', fileList)
  // v-model 已经处理了 imageList 的更新，这里不需要再手动操作
  // 可以在这里触发其他业务逻辑
}

// 扫描批次码功能，参考completed-inspection页面的实现
const scanBatchNo = () => {
  console.log('扫描批次码')
  uni.scanCode({
    success: (res) => {
      code.value = res.result // 将扫描结果赋值给批次码字段
    },
    fail: (err) => {
      console.error('扫码失败:', err)
      uni.showToast({
        title: '扫码失败',
        icon: 'none',
      })
    },
  })
}

// 提交表单
const handleSubmit = async () => {
  if (!materialId.value) {
    uni.showToast({
      title: '请选择来料名称',
      icon: 'none',
    })
    return
  }

  if (!code.value) {
    uni.showToast({
      title: '请输入批次码',
      icon: 'none',
    })
    return
  }

  if (!quantity.value) {
    uni.showToast({
      title: '请输入数量',
      icon: 'none',
    })
    return
  }

  if (!supplierName.value) {
    uni.showToast({
      title: '请输入供应商名称',
      icon: 'none',
    })
    return
  }

  // 使用提取的方法将图片列表转换为逗号分隔的字符串格式
  const forRemarkString = convertImageListToString(imageList.value)

  const resp = await postIncoming({
    materialId: materialId.value,
    batchNo: code.value,
    supplier: supplierName.value,
    inspector: inspector.value,
    quantity: quantity.value,
    rejectQuantity: rejectQuantity.value,
    remark: remark.value,
    forRemark: forRemarkString, // 传递转换后的图片路径字符串
  })

  // 模拟提交成功
  uni.showLoading({
    title: '提交中...',
  })

  setTimeout(() => {
    uni.hideLoading()
    uni.showToast({
      title: '保存成功',
      icon: 'success',
      duration: 2000,
      success: () => {
        setTimeout(() => {
          // 返回上一页
          uni.navigateTo({
            url: '/pages/quality/incoming/index',
          })
        }, 2000)
      },
    })
  }, 1500)
}
</script>

<style lang="scss" scoped>
/**
 * 来料检表单页面样式
 * 使用统一的设计系统样式
 * 更新时间: 2025-06-18
 */

// 引入设计系统变量
@import '@/style/design-system.scss';

// 页面容器使用标准样式类，无需额外定义
// .page-container 已在 components.scss 中定义

// 表单区域使用标准样式类，无需额外定义
// .form-section 已在 components.scss 中定义

// 区块标题使用标准样式类，无需额外定义
// .section-title 已在 components.scss 中定义

// 扫描输入框使用标准样式类，无需额外定义
// .input-with-scan 已在 components.scss 中定义

// 底部操作区域使用标准样式类，无需额外定义
// .action-section 已在 components.scss 中定义

// 图片上传容器使用标准样式类，无需额外定义
// .upload-container 已在 components.scss 中定义

// 如果需要页面特定的样式调整，可以在这里添加
// 但应该尽量使用设计系统中的标准样式

// 质量检验模块特有的样式调整
.form-section {
  // 为质量检验模块添加左边框标识
  border-left: 4rpx solid $quality-primary;
}

// 扫描图标颜色调整为质量检验模块主色
.scan-icon {
  color: $quality-primary !important;
}

// 确保上传容器的间距符合设计规范
.upload-container {
  margin-top: $spacing-sm;
  padding: 0 $spacing-lg;
}
</style>
