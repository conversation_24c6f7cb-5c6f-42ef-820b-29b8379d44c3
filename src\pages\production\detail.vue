<route lang="json5">
{
  type: 'page',
  needLogin: true,
  style: {
    navigationBarTitleText: '生产计划详情',
  },
}
</route>

<template>
  <view class="production-detail">
    <!-- 顶部计划概览 -->
    <view class="plan-overview">
      <view class="plan-header">
        <view class="plan-title">{{ planData.name }}</view>
        <view class="plan-status" :class="getStatusClass(planData.status)">
          {{ getStatusText(planData.status) }}
        </view>
      </view>
      <view class="plan-description">{{ planData.description }}</view>

      <!-- 进度条 -->
      <view class="progress-bar-wrapper">
        <progress-bar
          :progressValue="planData.progressValue"
          barHeight="12px"
          :barWidth="100"
          barBackGroundColor="#F3F3F3"
          barBackColor="#5EC691"
        />
      </view>

      <!-- 进度数据 -->
      <view class="progress-stats">
        <view class="stat-item">
          <view class="stat-label">完成率</view>
          <view class="stat-value">{{ planData.progressValue }}%</view>
        </view>
        <view class="stat-item">
          <view class="stat-label">已完成</view>
          <view class="stat-value">{{ planData.completeQuantity }}</view>
        </view>
        <view class="stat-item">
          <view class="stat-label">计划完成</view>
          <view class="stat-value">{{ planData.planQuantity }}</view>
        </view>
      </view>
    </view>

    <!-- 生产信息 -->
    <view class="plan-info">
      <view class="info-header">
        <view class="info-title">生产信息</view>
      </view>

      <view class="info-row">
        <view class="info-label">计划编号</view>
        <view class="info-value">{{ planData.proPlanNum }}</view>
      </view>

      <view class="info-row">
        <view class="info-label">订单编号</view>
        <view class="info-value">{{ planData.orderNum }}</view>
      </view>

      <view class="info-row">
        <view class="info-label">设备</view>
        <view class="info-value">{{ planData.equipment }}</view>
      </view>

      <view class="info-row">
        <view class="info-label">品名</view>
        <view class="info-value">{{ planData.productName }}</view>
      </view>

      <view class="info-row">
        <view class="info-label">计划产量</view>
        <view class="info-value">{{ planData.planQuantity }}</view>
      </view>

      <view class="info-row">
        <view class="info-label">实际产量</view>
        <view class="info-value">{{ planData.completeQuantity }}</view>
      </view>

      <view class="info-row">
        <view class="info-label">不良品</view>
        <view class="info-value">{{ planData.defQuantity }}</view>
      </view>

      <view class="info-row">
        <view class="info-label">不良品率</view>
        <view class="info-value">{{ planData.defectRate }}%</view>
      </view>

      <view class="info-row">
        <view class="info-label">交期</view>
        <view class="info-value">{{ planData.deliveryDate }}</view>
      </view>

      <view class="info-row">
        <view class="info-label">计划状态</view>
        <view class="info-value" :class="getStatusClass(planData.status)">
          {{ getStatusText(planData.status) }}
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import ProgressBar from '@/components/charts/ProgressBar.vue'

// 获取路由参数
const planId = ref('')
onMounted(() => {
  const query = uni.getLaunchOptionsSync().query
  if (query && query.id) {
    planId.value = query.id
    loadPlanDetail(planId.value)
  }
})

// 计划数据
const planData = ref({
  id: '',
  name: '生产计划1',
  description: '这是一个详细的生产计划描述，包含了生产计划的主要内容和目标。',
  status: 1, // 0-未开始，1-进行中，2-已完成
  proPlanNum: 'PP2025001001',
  orderNum: 'ORD2025001001',
  equipment: 'CNC-001',
  productName: '铝合金零件A',
  planQuantity: 100,
  completeQuantity: 60,
  defQuantity: 5,
  defectRate: 0,
  deliveryDate: '2025-06-15',
  progressValue: 0,
})

// 加载计划详情
const loadPlanDetail = (id: string) => {
  // 模拟API请求
  setTimeout(() => {
    // 根据ID生成不同的模拟数据
    const status = Math.floor(Math.random() * 3)
    const planQuantity = 100
    let completeQuantity = 0

    if (status === 0) {
      // 未开始
      completeQuantity = 0
    } else if (status === 1) {
      // 进行中
      completeQuantity = Math.floor(planQuantity * (Math.random() * 0.8 + 0.1))
    } else {
      // 已完成
      completeQuantity = planQuantity
    }

    const defQuantity = Math.floor(completeQuantity * Math.random() * 0.1)
    const defectRate =
      completeQuantity > 0 ? Number(((defQuantity / completeQuantity) * 100).toFixed(2)) : 0
    const progressValue = ((completeQuantity / planQuantity) * 100).toFixed(0)

    planData.value = {
      id,
      name: `生产计划${id.substring(id.length - 3)}`,
      description: '这是一个详细的生产计划描述，包含了生产计划的主要内容和目标。',
      status,
      proPlanNum: `PP2025${id.substring(id.length - 6)}`,
      orderNum: `ORD2025${Math.floor(Math.random() * 10000)}`,
      equipment: `CNC-${Math.floor(Math.random() * 10) + 1}`.padStart(3, '0'),
      productName: `铝合金零件${String.fromCharCode(65 + Math.floor(Math.random() * 26))}`,
      planQuantity,
      completeQuantity,
      defQuantity,
      defectRate,
      deliveryDate: `2025-0${Math.floor(Math.random() * 9) + 1}-${Math.floor(Math.random() * 28) + 1}`,
      progressValue: parseInt(progressValue),
    }
  }, 500)
}

// 获取状态文本
const getStatusText = (status: number) => {
  switch (status) {
    case 0:
      return '未开始'
    case 1:
      return '进行中'
    case 2:
      return '已完成'
    default:
      return '未知状态'
  }
}

// 获取状态类名
const getStatusClass = (status: number) => {
  switch (status) {
    case 0:
      return 'not-started'
    case 1:
      return 'in-progress'
    case 2:
      return 'completed'
    default:
      return ''
  }
}
</script>

<style scoped>
.production-detail {
  width: 94%;
  padding-bottom: 20px;
  margin: 0 auto;
}
/* 计划概览 */
.plan-overview {
  padding: 16px;
  margin-top: 10px;
  background-color: #ffffff;
  border-radius: 4px;
}

.plan-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.plan-title {
  font-size: 16px;
  font-weight: 500;
  color: #284cea;
}

.plan-status {
  font-size: 12px;
  font-weight: 400;
}

.plan-description {
  display: -webkit-box;
  margin-bottom: 16px;
  overflow: hidden;
  font-size: 14px;
  line-height: 1.5;
  color: #333333;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

.progress-bar-wrapper {
  margin-bottom: 10px;
}

.progress-stats {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  width: 33.33%;
}

.stat-label {
  margin-bottom: 4px;
  font-size: 10px;
  color: #999999;
}

.stat-value {
  font-size: 14px;
  font-weight: 500;
  color: #5ec691;
}
/* 生产信息 */
.plan-info {
  padding: 20px;
  margin-top: 10px;
  background-color: #ffffff;
  border-radius: 4px;
}

.info-header {
  margin-bottom: 15px;
}

.info-title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
}

.info-row {
  display: flex;
  margin-bottom: 10px;
}

.info-label {
  width: 70px;
  margin-right: 10px;
  font-size: 14px;
  color: #999999;
  text-align: justify;
  text-align-last: justify;
}

.info-value {
  flex: 1;
  font-size: 14px;
  line-height: 1.5;
  color: #333333;
}
/* 状态颜色 */
.completed {
  color: #36b876;
}

.in-progress {
  color: #f99e38;
}

.not-started {
  color: #b5b5b5;
}
</style>
