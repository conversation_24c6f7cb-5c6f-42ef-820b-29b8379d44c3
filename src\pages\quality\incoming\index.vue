<route lang="json5">
{
  needLogin: true,
  style: {
    navigationBarTitleText: '来料检管理',
    enablePullDownRefresh: true,
  },
}
</route>

<template>
  <view class="container">
    <!-- 使用封装的调机列表组件 -->
    <view class="list-section">
      <wd-cell-group v-for="item in list" :key="item.materialName">
        <wd-card type="rectangle">
          <!-- 自定义标题，包含追溯码和打印按钮 -->
          <template #title>
            <view class="card-header">
              <text class="card-title">追溯码: {{ item.traceCode }}</text>
              <wd-button
                type="primary"
                size="small"
                plain
                @click.stop="handlePrint(item)"
                class="print-btn"
              >
                <wd-icon name="print" size="14px" />
                打印
              </wd-button>
            </view>
          </template>

          <wd-cell title="物料名称" :value="item.materialName" />
          <wd-cell title="批次号" :value="item.code" />
          <wd-cell title="供应商名称" :value="item.supplierName" />
          <wd-cell title="物料数量" :value="item.quantity" />
          <wd-cell title="不良品数量" :value="item.rejectQuantity" />
          <wd-cell title="质检人员" :value="item.inspector" />
          <wd-cell title="来料时间" :value="item.receivedAt" />
          <wd-cell title="备注" :value="item.remark" />
        </wd-card>
      </wd-cell-group>
      <wd-loadmore :state="loadMoreState" @reload="loadMore" />
    </view>
    <!-- 底部按钮 -->
    <view class="bottom-button">
      <wd-button type="primary" block @click="goToForm">新增来料记录</wd-button>
    </view>

    <!-- 打印标签弹窗 -->
    <print-label
      :show="showPrintPopup"
      :material-name="printData.materialName"
      :time="printData.time"
      :trace-code="printData.traceCode"
      @update:show="showPrintPopup = $event"
      @print="handlePrintConfirm"
      @cancel="handlePrintCancel"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { IIncomingItem } from '@/service/incoming/types'
import { getIncomingList } from '@/service/incoming'
import { onLoad, onReachBottom } from '@dcloudio/uni-app'
import { LoadMoreState } from 'wot-design-uni/components/wd-loadmore/types'
import { useUserStore } from '@/store'
import PrintLabel from '@/components/common/PrintLabel.vue'

// 分页相关参数
const pageSize = 10
let currentPage = 0
let totalPage = 10
const loadMoreState = ref<LoadMoreState>('loading')

// 添加loading状态
const loading = ref(false)
// 调机列表
const list = ref<IIncomingItem[]>([])

// 打印标签弹窗相关数据
const showPrintPopup = ref(false)
const printData = ref({
  materialName: '',
  time: '',
  traceCode: '',
})

// 请求列表数据
const loadData = () => {
  loading.value = true
  try {
    getIncomingList({
      inspector: useUserStore().userInfo.user.username,
      page: currentPage++,
      size: pageSize, // 参数名应该改为size
    })
      .then((res) => {
        const newData = res.data.content
        list.value = [...list.value, ...newData]
        totalPage = Math.ceil(res.data.totalElements / pageSize)
        if (currentPage === totalPage) {
          loadMoreState.value = 'finished'
        }
      })
      .catch((err) => {
        console.log(err)
        loadMoreState.value = 'error'
      })
  } catch (error) {
    loadMoreState.value = 'error'
  } finally {
    loading.value = false
  }
}

// 加载更多
const loadMore = () => {
  loadData()
}

onReachBottom(() => {
  if (currentPage < totalPage) {
    loadMore()
  } else {
    loadMoreState.value = 'finished'
  }
})

onLoad(() => {
  loadData()
})

// 前往表单页面
const goToForm = () => {
  uni.navigateTo({
    url: '/pages/quality/incoming/form',
  })
}

// 打印追溯码标签
const handlePrint = (item: IIncomingItem) => {
  // 阻止事件冒泡，防止触发卡片点击事件
  console.log('打印追溯码:', item.traceCode)

  // 设置打印数据并显示打印标签弹窗
  printData.value = {
    materialName: item.materialName,
    time: item.receivedAt
      ? new Date(item.receivedAt).toLocaleDateString()
      : new Date().toLocaleDateString(),
    traceCode: item.traceCode,
  }
  showPrintPopup.value = true
}

// 确认打印
const handlePrintConfirm = () => {
  // 执行实际的打印逻辑
  console.log('确认打印:', printData.value)

  // 显示成功提示
  uni.showToast({
    title: '打印任务已发送',
    icon: 'success',
    duration: 2000,
  })

  // 关闭弹窗
  showPrintPopup.value = false

  // TODO: 实际的打印逻辑
  // 可能需要调用后端API生成打印数据
  // 或者调用设备的打印接口
}

// 取消打印
const handlePrintCancel = () => {
  // 关闭弹窗
  showPrintPopup.value = false
}
</script>

<style lang="scss">
.container {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  min-height: 100vh;
  padding-bottom: 120rpx; /* 为底部按钮留出空间 */
  overflow-x: hidden;
  background-color: #f5f5f5;

  & > view {
    box-sizing: border-box;
    width: 100%;
    padding: 0;
    margin: 0;
    list-style-type: none;
  }
}

.search-bar {
  position: sticky;
  top: 0;
  z-index: 10;
  box-sizing: border-box;
  width: 100%;
  padding: 20rpx 30rpx;
  margin: 0;
  background-color: #ffffff;
}

.bottom-button {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 10;
  width: 100%;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-top: 1px solid #ebedf0;
}
/* 卡片标题样式 */
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.card-title {
  flex: 1;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
/* 打印按钮样式 */
.print-btn {
  display: flex;
  gap: 8rpx;
  align-items: center;
  padding: 8rpx 16rpx !important;
  font-size: 24rpx !important;
  border-radius: 8rpx !important;

  :deep(.wd-button__text) {
    display: flex;
    gap: 8rpx;
    align-items: center;
  }
}
</style>
