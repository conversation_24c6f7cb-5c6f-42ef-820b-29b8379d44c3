/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-pages

interface NavigateToOptions {
  url: "/pages/login/login" |
       "/pages/index/index" |
       "/pages/about-us/index" |
       "/pages/arrangement/arrange" |
       "/pages/arrangement/tasks" |
       "/pages/code/index" |
       "/pages/message/index" |
       "/pages/my/index" |
       "/pages/production/detail" |
       "/pages/production/progress" |
       "/pages/report/work" |
       "/pages/report/workList" |
       "/pages/machine/casting/form" |
       "/pages/machine/casting/index" |
       "/pages/machine/cnc/form" |
       "/pages/machine/cnc/index" |
       "/pages/my/myTask/index" |
       "/pages/pick/clean/form" |
       "/pages/pick/clean/index" |
       "/pages/pick/cnc/form" |
       "/pages/pick/cnc/index" |
       "/pages/pick/pressure/form" |
       "/pages/pick/pressure/index" |
       "/pages/quality/completed-inspection/detail" |
       "/pages/quality/completed-inspection/form" |
       "/pages/quality/completed-inspection/index" |
       "/pages/quality/incoming/form" |
       "/pages/quality/incoming/index" |
       "/pages/quality/outsource/form" |
       "/pages/quality/outsource/index";
}
interface RedirectToOptions extends NavigateToOptions {}

interface SwitchTabOptions {
  url: "/pages/index/index" | "/pages/code/index" | "/pages/my/index"
}

type ReLaunchOptions = NavigateToOptions | SwitchTabOptions;

declare interface Uni {
  navigateTo(options: UniNamespace.NavigateToOptions & NavigateToOptions): void;
  redirectTo(options: UniNamespace.RedirectToOptions & RedirectToOptions): void;
  switchTab(options: UniNamespace.SwitchTabOptions & SwitchTabOptions): void;
  reLaunch(options: UniNamespace.ReLaunchOptions & ReLaunchOptions): void;
}
