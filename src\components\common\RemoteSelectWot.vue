<template>
  <view class="remote-select-container">
    <!-- 主输入框，用于显示已选值和触发弹窗 -->
    <wd-input
      v-model="selectedLabel"
      :placeholder="placeholder"
      readonly
      use-suffix-slot
      allow-clear
      @click="openPopup"
      @clear="handleClear"
    >
      <template #suffix>
        <wd-icon name="arrow-down" />
      </template>
    </wd-input>

    <!-- 底部弹出层 -->
    <wd-popup v-model="showPopup" position="bottom" custom-style="height: 70vh;">
      <view class="popup-content">
        <!-- 弹窗内的搜索框 -->
        <view class="search-bar">
          <wd-input
            v-model="keyword"
            placeholder="请输入关键词搜索"
            prefix-icon="search"
            clearable
            @input="handleSearch"
          />
        </view>

        <!-- 滚动列表区域 -->
        <scroll-view scroll-y class="scroll-view" @scrolltolower="handleScrollToLower">
          <wd-cell-group v-if="list.length > 0" border>
            <wd-cell
              v-for="item in list"
              :key="item[propsConfig.value]"
              :title="item[propsConfig.label]"
              clickable
              @click="handleSelect(item)"
            />
          </wd-cell-group>

          <!-- 加载与状态提示 -->
          <view class="status-footer">
            <wd-loading v-if="loading" custom-class="loading-center" />
            <view v-if="!loading && hasMore && list.length > 0" class="status-text">
              上拉加载更多
            </view>
            <view v-if="!hasMore && list.length > 0" class="status-text">没有更多数据了</view>
            <wd-card v-if="!loading && list.length === 0" description="暂无数据" />
          </view>
        </scroll-view>
      </view>
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
import { computed, defineEmits, defineProps, reactive, ref, watch } from 'vue'

const props = defineProps({
  // v-model
  modelValue: {
    type: [String, Number],
    default: '',
  },
  // 占位符
  placeholder: {
    type: String,
    default: '请选择',
  },
  // 自定义label和value的key
  props: {
    type: Object,
    default: () => ({
      label: 'label',
      value: 'value',
    }),
  },
  // 远程请求函数
  remoteMethod: {
    type: Function,
    required: true,
  },
})

const emit = defineEmits(['update:modelValue', 'change'])

// 内部状态
const showPopup = ref(false)
const keyword = ref('')
const list = ref([])
const loading = ref(false)
const hasMore = ref(true)
const selectedLabel = ref('')

// 将 props.props 转换成计算属性，避免模板中访问深层对象
const propsConfig = computed(() => props.props)

const pagination = reactive({
  page: 0,
  pageSize: 15, // 每页数量可以适当增加
})

// --- 网络请求 ---
const fetchData = async () => {
  if (loading.value || !hasMore.value) return

  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.pageSize,
      blurry: keyword.value,
    }
    // 调用父组件传入的请求方法
    const res = await props.remoteMethod(params)
    console.log('res', res)
    // 假设接口返回格式为 { data: [], total: 100 }
    const resData = res.content || []
    const total = res.totalElements || 0

    if (pagination.page === 1) {
      list.value = resData
    } else {
      list.value = [...list.value, ...resData]
    }

    // 判断是否还有更多数据
    hasMore.value = list.value.length < total
  } catch (error) {
    console.error('Failed to fetch data:', error)
    // 可以在这里添加错误提示
  } finally {
    loading.value = false
  }
}

// --- 事件处理 ---

const openPopup = () => {
  showPopup.value = true
}

// 搜索框输入（防抖）
let searchTimer = null
const handleSearch = () => {
  clearTimeout(searchTimer)
  searchTimer = setTimeout(() => {
    resetAndLoad()
  }, 300)
}

// 滚动到底部加载下一页
const handleScrollToLower = () => {
  if (hasMore.value && !loading.value) {
    pagination.page++
    fetchData()
  }
}

// 选择列表项
const handleSelect = (item) => {
  selectedLabel.value = item[propsConfig.value.label]
  emit('update:modelValue', item[propsConfig.value.value])
  emit('change', item) // 抛出完整的选中对象
  showPopup.value = false
}

// 清除已选内容
const handleClear = () => {
  selectedLabel.value = ''
  emit('update:modelValue', '')
  emit('change', null)
}

// --- 辅助函数 ---

// 重置状态并加载第一页数据
const resetAndLoad = () => {
  pagination.page = 0
  list.value = []
  hasMore.value = true
  fetchData()
}

// --- 监听 ---

// 监听弹窗打开，打开时加载数据
watch(showPopup, (newVal) => {
  if (newVal) {
    resetAndLoad()
  }
})

// 监听 v-model 的变化，用于外部清空或回显
// 注意：单纯从 value 回显 label 需要额外的逻辑（比如一次反向查询），这里只做了清空处理
watch(
  () => props.modelValue,
  (newVal) => {
    if (!newVal) {
      selectedLabel.value = ''
    }
  },
  { immediate: true },
)
</script>

<style lang="scss" scoped>
.popup-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f7f8fa;
}

.search-bar {
  padding: 10px;
  background-color: #fff;
  border-bottom: 1px solid #ebedf0;
}

.scroll-view {
  flex: 1;
  height: 100%;
}

.status-footer {
  padding: 15px 0;
}

.loading-center {
  // wot-design-uni 的 loading 组件可能需要手动居中
  display: flex;
  justify-content: center;
}

.status-text {
  font-size: 14px;
  color: #999;
  text-align: center;
}
</style>
