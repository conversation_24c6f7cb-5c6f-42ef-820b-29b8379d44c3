<route>
    {
      "name": "CleanPick",
      "style": {
        "navigationBarTitleText": "清洗领料单"
      }
    }
    </route>

<template>
  <view class="clean-pick-page">
    <!-- 表单内容 -->
    <view class="form-content">
      <!-- 登录人信息 -->
      <wd-cell-group title="登录人信息">
        <!-- 从本地存储获取登录人信息，然后使用wd-input填入信息，并且不能修改 -->
        <wd-input v-model="userName" placeholder="登录人信息" readonly />
      </wd-cell-group>

      <!-- 批次码信息 -->
      <wd-cell-group title="批次码信息">
        <!-- 参考completed-inspection页面的批次码扫描实现 -->
        <view class="input-with-scan">
          <wd-input v-model="batchNo" placeholder="请扫描批次码" class="scan-input" />
          <view class="scan-btn" @tap="scanBatchCode">
            <wd-icon name="scan" class="scan-icon" />
          </view>
        </view>
      </wd-cell-group>

      <!-- 原料名称 -->
      <wd-cell-group title="原料名称">
        <RemoteSelectWot
          v-model="materialId"
          placeholder="请选择原料名称"
          :remote-method="getMaterialPageFn"
          :props="{ label: 'materialName', value: 'id' }"
          @change="handleMaterialChange"
        ></RemoteSelectWot>
      </wd-cell-group>

      <!-- 材料数量 -->
      <wd-cell-group title="材料数量">
        <wd-input v-model="pickNum" type="number" placeholder="请输入数量" />
      </wd-cell-group>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-btn">
      <wd-button type="primary" block @click="submitForm">确认领料</wd-button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { postPickMaterial } from '@/service/pick'
import { useToast } from 'wot-design-uni'
import { getMaterialPageApi } from '@/service/material'
import RemoteSelectWot from '@/components/common/RemoteSelectWot.vue'
import { useUserStore } from '@/store/user'

// 路由实例
const router = useRouter()
const toast = useToast()

// 从本地存储获取登录人信息
const userStore = useUserStore()
const userName = ref(userStore.userInfo.user.username)

// 表单数据
const batchNo = ref('')
const materialId = ref()
const pickNum = ref()

function getMaterialPageFn(params) {
  return getMaterialPageApi(params)
}
function handleMaterialChange(item) {
  console.log('选择的数据', item)
  materialId.value = item.id
}

// 扫描批次码
const scanBatchCode = async () => {
  try {
    const result = await uni.scanCode({
      scanType: ['qrCode', 'barCode'],
    })
    batchNo.value = result.result
  } catch (error) {
    uni.showToast({
      title: '扫码失败',
      icon: 'none',
    })
  }
}

// 提交表单
const submitForm = async () => {
  if (!batchNo.value) {
    uni.showToast({
      title: '请扫描批次码',
      icon: 'none',
    })
    return
  }
  if (!materialId.value) {
    uni.showToast({
      title: '请输入原料名称',
      icon: 'none',
    })
    return
  }
  if (!pickNum.value) {
    uni.showToast({
      title: '请输入材料数量',
      icon: 'none',
    })
    return
  }

  const resp = await postPickMaterial({
    batchNo: batchNo.value,
    materialId: materialId.value,
    pickNum: pickNum.value,
    pickerId: userStore.userInfo.user.id,
    category: 'clean',
  })

  toast.success({
    msg: '保存成功',
  })

  // 保存成功后跳转
  setTimeout(() => {
    uni.navigateTo({
      url: '/pages/pick/clean/index',
    })
  }, 1500)
}
</script>

<style lang="scss" scoped>
.clean-pick-page {
  min-height: 100vh;
  padding-bottom: 120rpx;
  background-color: var(--wot-bg-color);
}

.form-content {
  padding: 32rpx;
}

.submit-btn {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 24rpx 32rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 扫描输入框样式，参考completed-inspection页面的实现 */
.input-with-scan {
  display: flex;
  align-items: center;
  padding-right: 15rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
}

.scan-input {
  flex: 1;
  background-color: transparent;
}

.scan-btn {
  padding: 10rpx;
}

.scan-icon {
  font-size: 40rpx;
  color: #4a90e2;
}
</style>
