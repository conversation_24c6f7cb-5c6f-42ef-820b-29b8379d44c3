export interface IMessage {
  id: number
  confirmed: number
  content: string
  createTime: string
  expired: number
  machineOrderNum: string
  readStatus: number
  scheduleDate: string
  shift: string
  taskType: number
}

export interface IMessageParams {
  page: number
  size: number
  userId?: number
  readStatus?: number
}

export interface IMessageRes {
  content: IMessage[]
  totalElements: number
}
