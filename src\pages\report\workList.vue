<route lang="json5">
{
  needLogin: true,
  style: {
    navigationBarTitleText: '报工记录',
  },
}
</route>
<template>
  <view>
    <view v-if="machineTaskId !== 0">
      <wd-card>
        <wd-cell-group>
          <wd-cell title="总生产工时" :value="workData.productHours + ' h'" />
          <wd-cell title="总停机工时" :value="workData.changeHours + ' h'" />
          <wd-cell title="报工总数" :value="workData.sum + ' 个'" />
          <wd-cell title="机台显示总数" :value="workData.displaySum + ' 件'" />
          <wd-cell title="不良品总数" :value="workData.defSum + ' 个'" />
          <wd-cell title="开始时间" :value="workData.startTime" />
          <wd-cell title="结束时间" :value="workData.endTime" />
        </wd-cell-group>
      </wd-card>
    </view>
    <view v-if="machineTaskId === 0">
      <wd-calendar
        type="daterange"
        label="日期范围"
        align="center"
        v-model="dateRange"
        allow-same-day
        @confirm="handleConfirm"
      />
    </view>
    <view class="report-list-container">
      <wd-card v-for="item in workReportList" :key="item.id">
        <wd-cell-group>
          <wd-cell title="停机工时" :value="item.changeHours + ' h'" />
          <wd-cell title="生产工时" :value="item.productHours + ' h'" />
          <wd-cell title="不良品数" :value="item.defQuantity + ' 个'" />
          <wd-cell title="报工时间" :value="item.reportTime" />
          <wd-cell title="合计数量" :value="item.reportQuantity" />
        </wd-cell-group>
      </wd-card>
      <wd-loadmore :state="state" @reload="loadMore" />
    </view>
  </view>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { mobile, getWorkReportList } from '@/service/report/workList'
import { timestampsToDateStrings } from '@/utils/time'
import { LoadMoreState } from 'wot-design-uni/components/wd-loadmore/types'
import { onReachBottom } from '@dcloudio/uni-app'

const workData = ref<any>({})
const today = new Date().getTime()
const dateRange = ref<number[]>([today, today])

const workReportList = ref<any[]>([])
const state = ref<LoadMoreState>('loading')
let page = 0
const pageSize = 10
let totalPage = 0
const machineTaskId = ref<number>(0)

onReachBottom(() => {
  if (page < totalPage) {
    loadMore()
  } else {
    state.value = 'finished'
  }
})

onLoad((option) => {
  if (option.machineTaskId) {
    machineTaskId.value = Number(option.machineTaskId)
    callMobile()
  } else {
    loadMore()
  }
})

const callMobile = () => {
  mobile({
    machineTaskId: machineTaskId.value,
  }).then((res) => {
    workData.value = res.data
    workReportList.value = res.data.content
    state.value = 'finished'
  })
}

const loadMore = () => {
  callWorkReportList(page++)
}

const callWorkReportList = (page: number) => {
  const [startTime, endTime] = timestampsToDateStrings(dateRange.value)
  getWorkReportList({
    page,
    size: pageSize,
    startTime: startTime + ' 00:00:00',
    endTime: endTime + ' 23:59:59',
  })
    .then((res) => {
      workReportList.value = [...workReportList.value, ...res.data.content]
      totalPage = Math.ceil(res.data.totalElements / pageSize)
      console.log(page)
      console.log(totalPage)
      if (res.data.totalElements === 0 || page === totalPage - 1) {
        state.value = 'finished'
      } else {
        state.value = 'loading'
      }
    })
    .catch((err) => {
      console.log(err)
      state.value = 'error'
    })
}
const handleConfirm = () => {
  page = 0
  state.value = 'loading'
  workReportList.value = []
  loadMore()
}
const back = () => {
  console.log('back')
  uni.switchTab({
    url: '/pages/index/index',
  })
}
</script>

<style lang="scss" scoped>
.report-list-container {
  min-height: 100vh; /* 根据需求调整最小高度，例如80视口高度 */
  padding: 16rpx 16rpx; /* 可选：根据设计添加内边距 */
  overflow-y: auto; /* 内容超过高度时显示滚动条 */
}
</style>
