<route lang="json5" type="page">
{
  layout: 'default',
  needLogin: true,
  style: {
    navigationBarTitleText: '关于我们',
    backgroundColor: '#f3f4f6',
  },
}
</route>

<template>
  <view class="about-page">
    <!-- 公司Logo和名称 -->
    <view class="company-header">
      <view class="company-name">苏州车邦智能科技有限公司</view>
      <view class="version-info">版本号：v1.0</view>
    </view>

    <!-- 产品介绍卡片 -->
    <view class="product-card">
      <view class="card-title">
        <wd-icon name="info" size="20px" color="#667eea" />
        <text class="title-text">产品介绍</text>
      </view>
      <view class="product-name">车邦数字化生产协同管理平台</view>
      <view class="product-description">
        专注于离散制造，采用云端部署，多端适配，覆盖设备管理、智能排产、协同生产、仓储管理、质量检验、员工绩效等核心业务模块，助力制造业实现从客户下单、原料采购和生产交付的全流程数字化管理。
      </view>
    </view>

    <!-- 核心功能模块 -->
    <view class="features-card">
      <view class="card-title">
        <wd-icon name="tools" size="20px" color="#667eea" />
        <text class="title-text">核心功能</text>
      </view>
      <view class="features-grid">
        <view class="feature-item">
          <wd-icon name="calendar" size="24px" color="#19be6b" />
          <text class="feature-text">设备管理</text>
        </view>
        <view class="feature-item">
          <wd-icon name="clock" size="24px" color="#ff9900" />
          <text class="feature-text">智能排产</text>
        </view>
        <view class="feature-item">
          <wd-icon name="tools" size="24px" color="#fa3534" />
          <text class="feature-text">协同生产</text>
        </view>
        <view class="feature-item">
          <wd-icon name="download" size="24px" color="#2979ff" />
          <text class="feature-text">仓储管理</text>
        </view>
        <view class="feature-item">
          <wd-icon name="search" size="24px" color="#9c27b0" />
          <text class="feature-text">质量检验</text>
        </view>
        <view class="feature-item">
          <wd-icon name="notification" size="24px" color="#1296db" />
          <text class="feature-text">员工绩效</text>
        </view>
      </view>
    </view>

    <!-- 技术特点 -->
    <view class="tech-card">
      <view class="card-title">
        <wd-icon name="scan" size="20px" color="#667eea" />
        <text class="title-text">技术特点</text>
      </view>
      <view class="tech-list">
        <view class="tech-item">
          <wd-icon name="check" size="16px" color="#19be6b" />
          <text class="tech-text">云端部署，安全可靠</text>
        </view>
        <view class="tech-item">
          <wd-icon name="check" size="16px" color="#19be6b" />
          <text class="tech-text">多端适配，随时随地</text>
        </view>
        <view class="tech-item">
          <wd-icon name="check" size="16px" color="#19be6b" />
          <text class="tech-text">专注离散制造行业</text>
        </view>
        <view class="tech-item">
          <wd-icon name="check" size="16px" color="#19be6b" />
          <text class="tech-text">全流程数字化管理</text>
        </view>
      </view>
    </view>

    <!-- 联系信息 -->
    <view class="contact-card">
      <view class="card-title">
        <wd-icon name="phone" size="20px" color="#667eea" />
        <text class="title-text">联系我们</text>
      </view>
      <view class="contact-info">
        <view class="contact-item">
          <text class="contact-label">公司地址：</text>
          <text class="contact-value">苏州市</text>
        </view>
        <view class="contact-item">
          <text class="contact-label">技术支持：</text>
          <text class="contact-value">车邦智能科技</text>
        </view>
      </view>
    </view>

    <!-- 版权信息 -->
    <view class="copyright">
      <text class="copyright-text">© 2024 苏州车邦智能科技有限公司</text>
      <text class="copyright-text">All Rights Reserved</text>
    </view>
  </view>
</template>

<script lang="ts" setup>
// 关于我们页面逻辑
</script>

<style lang="scss" scoped>
.about-page {
  min-height: 100vh;
  padding: 16px;
  background-color: #f3f4f6;
}

/* 公司头部信息 */
.company-header {
  text-align: center;
  padding: 32px 16px;
  background-color: #ffffff;
  border-radius: 12px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.logo-container {
  margin-bottom: 16px;
}

.company-name {
  font-size: 20px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8px;
}

.version-info {
  font-size: 14px;
  color: #666666;
  padding: 4px 12px;
  background-color: #f0f0f0;
  border-radius: 12px;
  display: inline-block;
}

/* 卡片通用样式 */
.product-card,
.features-card,
.tech-card,
.contact-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.card-title {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.title-text {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  margin-left: 8px;
}

/* 产品介绍样式 */
.product-name {
  font-size: 18px;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 12px;
  text-align: center;
}

.product-description {
  font-size: 14px;
  line-height: 1.6;
  color: #666666;
  text-align: justify;
}

/* 功能模块网格 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 8px;
  border-radius: 8px;
  background-color: #f8f9fa;
}

.feature-text {
  font-size: 12px;
  color: #333333;
  margin-top: 8px;
  text-align: center;
}

/* 技术特点列表 */
.tech-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tech-item {
  display: flex;
  align-items: center;
}

.tech-text {
  font-size: 14px;
  color: #333333;
  margin-left: 8px;
}

/* 联系信息 */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.contact-item {
  display: flex;
  align-items: center;
}

.contact-label {
  font-size: 14px;
  color: #666666;
  min-width: 80px;
}

.contact-value {
  font-size: 14px;
  color: #333333;
  flex: 1;
}

/* 版权信息 */
.copyright {
  text-align: center;
  padding: 24px 16px;
  margin-top: 16px;
}

.copyright-text {
  display: block;
  font-size: 12px;
  color: #999999;
  line-height: 1.5;
}
</style>
