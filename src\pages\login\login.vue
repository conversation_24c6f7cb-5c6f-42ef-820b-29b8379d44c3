<route lang="json5" type="home">
{
  layout: 'default',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: 'MES系统登录',
  },
}
</route>

<template>
  <view class="mes-login-container">
    <!-- 背景网格和装饰 -->
    <view class="bg-grid"></view>
    <view class="bg-decoration">
      <view class="tech-line line-1"></view>
      <view class="tech-line line-2"></view>
      <view class="tech-line line-3"></view>
      <view class="gear-icon gear-1">⚙</view>
      <view class="gear-icon gear-2">⚙</view>
    </view>

    <!-- 主要内容区域 -->
    <view class="content-wrapper" :style="{ paddingTop: safeAreaInsets?.top + 'px' }">
      <!-- 系统标题区域 -->
      <view class="header-section">
        <view class="system-logo">
          <view class="logo-icon">
            <img src="@/static/images/svg/绿色工厂-copy.svg" alt="" width="88px" height="88px" />
          </view>
        </view>
        <view class="system-title">欢迎使用SMES系统</view>
        <view class="system-subtitle">Manufacturing Execution System</view>
      </view>

      <!-- 登录表单卡片 -->
      <view class="form-card">
        <view class="card-header">
          <view class="card-title">系统登录</view>
        </view>

        <view class="form-content">
          <view class="input-group">
            <view class="input-label">
              <view class="label-icon">
                <img src="@/static/images/svg/user.svg" alt="" width="20px" height="20px" />
              </view>
              <view class="label-text">用户名</view>
            </view>
            <wd-input v-model="username" placeholder="请输入工号或用户名" clearable />
          </view>

          <view class="input-group">
            <view class="input-label">
              <view class="label-icon">
                <img src="@/static/images/svg/password.svg" alt="" width="20px" height="20px" />
              </view>
              <view class="label-text">密码</view>
            </view>
            <wd-input v-model="password" placeholder="请输入登录密码" clearable show-password />
          </view>

          <wd-button
            type="primary"
            block
            @click="handleLogin"
            :loading="loading"
            size="large"
            class="login-btn"
          >
            <view class="btn-content">
              <view class="btn-text">{{ loading ? '登录中...' : '登录系统' }}</view>
            </view>
          </wd-button>
        </view>
      </view>

      <!-- 底部信息 -->
      <view class="footer-info">
        <view class="company-info">
          <view class="company-name">SMES智能制造管理平台</view>
          <view class="copyright"></view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { loginApi } from '@/service/login'
import { useUserStore } from '@/store'
import { useToast } from 'wot-design-uni'
import { encrypt, decrypt } from '@/utils/rsaEncrypt'
import { ref } from 'vue'
import { onShow } from '@dcloudio/uni-app'

const toast = useToast()
const userStore = useUserStore()
const loading = ref(false)
const safeAreaInsets = ref(uni.getSystemInfoSync().safeAreaInsets)

// 表单数据
const username = ref('')
const password = ref('')

// 登录处理
const handleLogin = async () => {
  try {
    console.log('resp')
    loading.value = true
    const resp = await loginApi({
      username: username.value,
      password: encrypt(password.value),
    })
    userStore.setUserInfo({
      ...resp.data.user,
      token: resp.data.token,
    })
    // 登录成功处理
    toast.success({
      msg: '登录成功，欢迎使用MES系统',
    })

    // 登录成功后跳转
    setTimeout(() => {
      uni.switchTab({
        url: '/pages/index/index',
      })
    }, 1500)
  } catch (error) {
    console.log('error', error)
    // 登录失败处理
    toast.error({
      msg: '登录失败，请检查用户名和密码',
    })
  } finally {
    loading.value = false
  }
}

onShow(() => {
  const token = userStore.userInfo?.token
  if (token) {
    uni.switchTab({
      url: '/pages/index/index',
    })
  }
})
</script>

<style lang="scss" scoped>
.mes-login-container {
  position: relative;
  min-height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, #6484e9 0%, #5c95e1 50%, #59a6e4 100%);
}

// 背景网格
.bg-grid {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  pointer-events: none;
  background-image: linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 20px 20px;
}

// 科技装饰线条
.bg-decoration {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  pointer-events: none;

  .tech-line {
    position: absolute;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.3), transparent);
    animation: scan 4s linear infinite;

    &.line-1 {
      top: 20%;
      right: 0;
      left: 0;
      animation-delay: 0s;
    }

    &.line-2 {
      top: 60%;
      right: 0;
      left: 0;
      animation-delay: 1.5s;
    }

    &.line-3 {
      top: 80%;
      right: 0;
      left: 0;
      animation-delay: 3s;
    }
  }

  .gear-icon {
    position: absolute;
    font-size: 24px;
    color: rgba(255, 255, 255, 0.1);
    animation: rotate 20s linear infinite;

    &.gear-1 {
      top: 15%;
      right: 20px;
    }

    &.gear-2 {
      bottom: 25%;
      left: 20px;
      animation-duration: 15s;
      animation-direction: reverse;
    }
  }
}

@keyframes scan {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.content-wrapper {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 40px 24px;
}

// 系统标题区域
.header-section {
  margin-bottom: 50px;
  text-align: center;

  .system-logo {
    margin-bottom: 16px;

    .logo-icon {
      margin-top: 30px;
      font-size: 48px;
      filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
    }
  }

  .system-title {
    margin-bottom: 8px;
    font-size: 28px;
    font-weight: bold;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: 1px;
  }

  .system-subtitle {
    margin-bottom: 8px;
    font-family: 'Arial', sans-serif;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
  }

  .version-info {
    display: inline-block;
    padding: 4px 12px;
    font-size: 12px;
    color: #00ffff;
    background: rgba(0, 255, 255, 0.2);
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 12px;
  }
}

// 毛玻璃卡片
.form-card {
  margin-bottom: 30px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 24px 0;

  .card-title {
    font-size: 20px;
    font-weight: bold;
    color: #ffffff;
  }

  .security-badge {
    display: flex;
    gap: 4px;
    align-items: center;
    padding: 4px 8px;
    background: rgba(0, 255, 0, 0.2);
    border: 1px solid rgba(0, 255, 0, 0.3);
    border-radius: 8px;

    .badge-icon {
      font-size: 12px;
    }

    .badge-text {
      font-size: 10px;
      color: #00ff00;
    }
  }
}

.form-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 24px;
}

.input-group {
  .input-label {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-bottom: 8px;

    .label-icon {
      font-size: 14px;
    }

    .label-text {
      font-size: 14px;
      font-weight: 500;
      color: rgba(255, 255, 255, 0.9);
    }
  }
}

// 自定义输入框样式
:deep(.mes-input) {
  .wd-input__inner {
    padding: 14px 16px !important;
    font-size: 16px !important;
    background: rgba(255, 255, 255, 0.9) !important;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;

    &:focus {
      background: rgba(255, 255, 255, 0.95) !important;
      border-color: #00bcd4 !important;
      box-shadow: 0 0 0 2px rgba(0, 188, 212, 0.2) !important;
    }
  }

  .wd-input__placeholder {
    color: rgba(0, 0, 0, 0.5) !important;
  }
}

// 登录按钮样式
:deep(.login-btn) {
  .wd-button {
    padding: 16px !important;
    font-size: 16px !important;
    font-weight: bold !important;
    background: linear-gradient(135deg, #00bcd4, #0097a7) !important;
    border: none !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 15px rgba(0, 188, 212, 0.4) !important;
    transition: all 0.3s ease !important;

    &:active {
      box-shadow: 0 2px 10px rgba(0, 188, 212, 0.4) !important;
      transform: translateY(1px) !important;
    }
  }
}

.btn-content {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;

  .btn-icon {
    font-size: 14px;
  }
}

// 底部信息
.footer-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
  margin-top: auto;

  .status-indicator {
    display: flex;
    gap: 8px;
    align-items: center;

    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;

      &.online {
        background: #00ff00;
        box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
        animation: pulse 2s infinite;
      }
    }

    .status-text {
      font-size: 12px;
      color: rgba(255, 255, 255, 0.7);
    }
  }

  .company-info {
    text-align: center;

    .company-name {
      margin-bottom: 4px;
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
    }

    .copyright {
      font-size: 12px;
      color: rgba(255, 255, 255, 0.5);
    }
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// 响应式适配
@media (max-width: 375px) {
  .content-wrapper {
    padding: 30px 20px;
  }

  .header-section {
    margin-bottom: 40px;

    .system-title {
      font-size: 24px;
    }
  }

  .form-content {
    padding: 20px;
  }
}
</style>
