<route lang="json5">
{
  needLogin: true,
  style: {
    navigationBarTitleText: 'CNC调机管理',
    enablePullDownRefresh: true,
  },
}
</route>

<template>
  <view class="container">
    <!-- 顶部搜索区域 -->
    <view class="search-bar">
      <wd-input
        v-model="searchKeyword"
        placeholder="请输入搜索关键词"
        clearable
        prefix-icon="search"
        @input="handleSearch"
      />
    </view>

    <!-- 使用封装的调机列表组件 -->
    <view class="list-section">
      <wd-cell-group v-for="item in list" :key="item.machineNumber">
        <wd-card title="调机记录" type="rectangle">
          <wd-cell title="设备编码" :value="item.machineNumber" />
          <wd-cell title="夹具编码" :value="item.partsName" />
          <wd-cell title="调机人员" :value="item.creatorName" />
          <wd-cell title="调机时间" :value="item.createTime" />
        </wd-card>
      </wd-cell-group>
      <wd-loadmore :state="loadMoreState" @reload="loadMore" />
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-button">
      <wd-button type="primary" block @click="goToForm">新增调机</wd-button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { IAdjustmentItem } from '@/service/adjustment/types'
import { getDeviceAdjustmentList } from '@/service/adjustment'
import { onLoad, onReachBottom } from '@dcloudio/uni-app'
import { LoadMoreState } from 'wot-design-uni/components/wd-loadmore/types'

// 分页相关参数
const pageSize = 10
let currentPage = 0
let totalPage = 10
const loadMoreState = ref<LoadMoreState>('loading')

// 添加loading状态
const loading = ref(false)
// 搜索关键词
const searchKeyword = ref('')
// 防抖定时器
let searchTimer: NodeJS.Timeout | null = null

// 调机列表
const list = ref<IAdjustmentItem[]>([])

// 加载数据函数，支持搜索和分页
const loadData = (isSearch = false) => {
  loading.value = true

  // 如果是搜索，重置分页状态
  if (isSearch) {
    currentPage = 0
    list.value = []
    loadMoreState.value = 'loading'
  }

  // 动态构建请求参数
  const params: any = {
    deviceNum: searchKeyword.value.trim(),
    page: currentPage, // 先使用当前页码，成功后再增加
    size: pageSize,
  }

  try {
    getDeviceAdjustmentList(params)
      .then((res) => {
        const newData = res.data.content

        // 如果是搜索，替换数据；如果是加载更多，追加数据
        if (isSearch) {
          list.value = newData
        } else {
          list.value = [...list.value, ...newData]
        }

        // 更新分页信息
        totalPage = Math.ceil(res.data.totalElements / pageSize)
        currentPage++ // 请求成功后才增加页码

        // 判断是否还有更多数据
        if (currentPage >= totalPage) {
          loadMoreState.value = 'finished'
        } else {
          loadMoreState.value = 'loading'
        }
      })
      .catch((err) => {
        console.log(err)
        loadMoreState.value = 'error'
      })
  } catch (error) {
    loadMoreState.value = 'error'
  } finally {
    loading.value = false
  }
}

// 加载更多数据
const loadMore = () => {
  if (currentPage < totalPage && loadMoreState.value !== 'loading') {
    loadData(false) // 加载更多，不是搜索
  }
}

onReachBottom(() => {
  if (currentPage < totalPage) {
    loadMore()
  } else {
    loadMoreState.value = 'finished'
  }
})

onLoad(() => {
  loadData(true) // 初始加载，按搜索处理
})

// 前往表单页面
const goToForm = () => {
  uni.navigateTo({
    url: '/pages/machine/cnc/form',
  })
}

// 处理搜索输入（带防抖）
const handleSearch = () => {
  console.log('搜索关键词:', searchKeyword.value)

  // 清除之前的定时器
  if (searchTimer) {
    clearTimeout(searchTimer)
  }

  // 设置防抖延迟
  searchTimer = setTimeout(() => {
    // 执行搜索，重置分页状态
    loadData(true)
  }, 500) // 500ms防抖延迟
}
</script>

<style lang="scss">
.container {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  min-height: 100vh;
  padding-bottom: 120rpx; /* 为底部按钮留出空间 */
  overflow-x: hidden;
  background-color: #f5f5f5;

  & > view {
    box-sizing: border-box;
    width: 100%;
    padding: 0;
    margin: 0;
    list-style-type: none;
  }
}

.search-bar {
  position: sticky;
  top: 0;
  z-index: 10;
  box-sizing: border-box;
  width: 100%;
  padding: 20rpx 30rpx;
  margin: 0;
  background-color: #ffffff;
}

.bottom-button {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 10;
  width: 100%;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-top: 1px solid #ebedf0;
}
</style>
