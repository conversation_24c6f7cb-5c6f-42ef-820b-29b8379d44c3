---
description:
globs:
alwaysApply: true
---

# 变更日志记录指南

## 变更记录要求

每次修改代码后，必须在项目根目录的`changeLog`文件夹中创建或更新当天的变更日志文件。变更日志有助于跟踪项目的历史变化，便于团队成员了解代码演进过程。

## 文件命名规则

变更日志文件必须按照日期命名，格式为：`changeLog年月日.md`，例如：`changeLog20240528.md`。

每天应该创建一个新的变更日志文件，所有当天的修改都记录在这个文件中。

## 记录格式

变更记录应遵循以下Markdown格式：

````markdown
# 变更日志 - 年月日

## [HH:mm:ss] [变更类型]: 简短描述

**作者**: 你的名字  
**文件**: 被修改的文件路径

### 变更描述

详细说明变更的原因和目的

### 变更详情

**原代码**:

```代码语言
// 这里是修改前的原始代码
```
````

**新代码**:

```代码语言
// 这里是修改后的新代码
```

````

## 使用时间

当需要在变更日志中记录具体时间时，必须使用`mcp_time-mcp_current_time`工具获取准确的当前时间。如果该工具调用失败，不要自己填写时间，而是留空等待手动填写。

```javascript
// 示例代码：获取当前时间（中国时区）
const currentTime = callFunction('mcp_time-mcp_current_time', {
  format: 'YYYY-MM-DD HH:mm:ss',
  timezone: 'Asia/Shanghai'
});
````

> **注意**：所有时间记录必须使用中国时区（Asia/Shanghai），确保团队成员看到一致的时间戳。

## 变更类型

变更类型应与Git提交类型保持一致：

- `feat` - 新功能
- `fix` - 修复Bug
- `refactor` - 代码重构
- `style` - 代码风格修改
- `docs` - 文档更新
- `perf` - 性能优化
- `test` - 测试相关
- `chore` - 其他修改

## 变更记录示例

````markdown
# 变更日志 - 2024年5月28日

## [10:30:45] feat: 添加用户登录表单验证功能

**作者**: 张三  
**文件**: src/pages/login/login.vue

### 变更描述

添加表单验证功能，防止用户提交空白或格式不正确的登录信息

### 变更详情

**原代码**:

```javascript
<script setup lang="ts">
const formData = reactive({
  username: '',
  password: ''
})

const handleLogin = () => {
  loginService.login(formData)
}
</script>
```
````

**新代码**:

```javascript
<script setup lang="ts">
const formData = reactive({
  username: '',
  password: ''
})

const formRules = {
  username: [{ required: true, message: '用户名不能为空' }],
  password: [
    { required: true, message: '密码不能为空' },
    { min: 6, message: '密码长度不能少于6位' }
  ]
}

const handleLogin = () => {
  formRef.value.validate().then(valid => {
    if (valid) {
      loginService.login(formData)
    }
  })
}
</script>
```

````

## 注意事项

1. 变更记录应使用Markdown格式以增强可读性和结构化
2. 原代码和新代码应包含足够的上下文，确保变更易于理解
3. 如果修改涉及多个文件，应分别列出每个文件的变更
4. 对于大型重构，可以适当精简代码示例，但必须保留关键变更
5. 每天开始工作时检查是否需要创建新的日期命名的变更日志文件
6. 代码块应指定适当的语言（如javascript, typescript, vue等）以获得语法高亮
---
description:
globs:
alwaysApply: true
---
# UI框架指南

## 主要UI框架

项目主要使用**wot-design-uni**作为UI组件库，这是一个基于uni-app的移动端组件库，由京东设计开发。

```typescript
// 使用wot-design-uni组件示例
<wd-button type="primary">按钮</wd-button>
<wd-cell title="标题" value="内容"></wd-cell>
````

## 分页组件

项目使用**z-paging**作为分页和下拉刷新、上拉加载组件：

```vue
<z-paging ref="paging" v-model="dataList" @query="queryList">
  <!-- 内容，如cell组件等 -->
</z-paging>
```

## 样式系统

样式系统由多个部分组成：

1. **UnoCSS** - 原子化CSS框架，通过类名直接应用样式

   ```html
   <view class="flex items-center justify-between p-4"></view>
   ```

2. **SCSS** - 全局样式定义在`src/style`目录下

3. **uni.scss** - uni-app内置样式变量

## 图标系统

项目使用**Iconify**作为图标库，配置了Carbon图标集：

```
@iconify-json/carbon
```

## 适配策略

项目采用uni-app跨平台开发，支持：

- H5
- 微信小程序
- App
- 其他小程序平台

通过条件编译实现不同平台的适配：

```vue
<!-- #ifdef H5 -->
<view>H5 环境特有内容</view>
<!-- #endif -->

<!-- #ifdef MP-WEIXIN -->
<view>微信小程序特有内容</view>
<!-- #endif -->
```

## 通用页面模式

### 列表页面

列表页面通常包含以下几个部分：

1. **顶部搜索栏** - 使用`wd-input`组件，添加搜索图标
2. **内容列表** - 使用`z-paging`组件实现下拉刷新和上拉加载
3. **列表项** - 使用卡片式布局，包含标题、状态标签、详情信息和时间等
4. **底部按钮** - 固定在底部，使用`wd-button`组件，通常为全宽按钮

```vue
<!-- 列表页面示例 -->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <wd-input placeholder="搜索" prefix-icon="search" />
  </view>

  <!-- 列表内容 -->
  <z-paging ref="paging" @query="queryList">
    <view class="list-item" v-for="item in list">
      <!-- 列表项内容 -->
    </view>
  </z-paging>

  <!-- 底部按钮 -->
  <view class="bottom-button">
    <wd-button type="primary" block>新增</wd-button>
  </view>
</view>
```

### 表单页面

表单页面通常包含以下几个部分：

1. **表单项组** - 白色背景，每个表单项包含标签和输入控件
2. **提交按钮** - 固定在底部，使用`wd-button`组件，通常为全宽按钮
3. **图片上传区** - 使用九宫格布局，带有添加按钮和删除功能

```vue
<!-- 表单页面示例 -->
<view class="container">
  <form @submit="handleSubmit">
    <!-- 表单内容 -->
    <view class="form-content">
      <view class="form-item" v-for="item in formItems">
        <view class="form-label">{{ item.label }}</view>
        <view class="form-value">
          <!-- 输入控件 -->
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-button">
      <wd-button type="primary" block form-type="submit">提交</wd-button>
    </view>
  </form>
</view>
```

## 质量检验模块UI规范

质量检验模块遵循以下特定UI规范：

### 色彩规范

- **主色调**: 品质检验使用蓝色系列 (#2979ff) 作为模块主色
- **状态颜色**:
  - 合格: 绿色 (#19be6b)
  - 不合格: 红色 (#fa3534)
  - 待检验: 橙色 (#ff9900)

### 来料检验列表页

- 采用卡片式布局，突出显示材料名称和检验状态
- 使用标签显示检验状态，采用对应的状态颜色
- 列表项中显示关键信息：批次号、数量、不良品数量、检验人员
- 底部固定"新增来料检验"按钮

### 来料检验表单

- 表单项布局为上下结构：标签在上，输入控件在下
- 必填项标签带有红色星号标识
- 系统自动填充的字段使用灰色背景区分
- 图片上传区支持多图上传，采用九宫格布局
- 表单底部固定提交按钮

### 其他质量检验页面

委外加工检验和完工检验页面应保持与来料检验页面相同的UI风格和交互模式，确保整个质量检验模块的用户体验一致性。

---

description:
globs:
alwaysApply: true

---

# 项目结构指南

## 目录结构

这个移动应用基于uni-app框架开发，使用TypeScript + Vue 3 + Vite构建。主要目录结构如下：

- `src/` - 源代码目录
  - `components/` - 可复用的组件
  - `hooks/` - 自定义钩子函数
  - `interceptors/` - 拦截器（请求、路由等）
  - `layouts/` - 页面布局模板
  - `pages/` - 主包页面
  - `pages-sub/` - 分包页面
  - `service/` - API服务
  - `static/` - 静态资源
  - `store/` - Pinia状态管理
  - `style/` - 全局样式
  - `types/` - TypeScript类型定义
  - `utils/` - 工具函数
- `env/` - 环境变量配置
- `vite-plugins/` - 自定义Vite插件

## 主要入口文件

- [main.ts](mdc:src/main.ts) - 应用入口文件
- [App.vue](mdc:src/App.vue) - 根组件
- [manifest.json](mdc:src/manifest.json) - uni-app配置文件
- [pages.json](mdc:src/pages.json) - 页面路由配置

## 构建工具

项目使用Vite作为构建工具，配置文件为[vite.config.ts](mdc:vite.config.ts)，支持多平台构建（H5、微信小程序、App等）。

---

description:
globs:
alwaysApply: true

---

# 开发工作流指南

## 开发环境准备

1. Node.js 18+
2. pnpm 7.30+ (项目使用pnpm管理依赖)

## 开发流程

1. 克隆项目后，执行`pnpm install`安装依赖
2. 选择合适的开发命令启动开发服务：
   - H5: `pnpm dev`或`pnpm dev:h5`
   - 微信小程序: `pnpm dev:mp-weixin`
   - App: `pnpm dev:app`

## 目录结构规范

- 页面组件放在`src/pages`目录
- 可复用组件放在`src/components`目录
- API服务放在`src/service`目录，按功能模块划分
- 状态管理放在`src/store`目录
- 工具函数放在`src/utils`目录

## Git提交规范

项目使用Conventional Commits规范进行Git提交，提交前会自动进行代码检查：

1. 使用`pnpm cz`命令代替`git commit`进行规范化提交
2. 提交前会自动运行lint-staged进行代码检查和格式化
3. 提交信息必须符合commitlint规范

参考提交类型：

```
feat: 新功能
fix: 修复Bug
docs: 文档更新
style: 代码风格修改
refactor: 代码重构
perf: 性能优化
test: 添加测试
build: 构建相关
ci: CI配置相关
chore: 其他修改
```

---

description:
globs:
alwaysApply: true

---

# 代码规范指南

## 风格指南

项目使用严格的代码风格检查，包括：

- **ESLint** - JavaScript/TypeScript代码风格检查，配置文件为 [.eslintrc.cjs](mdc:.eslintrc.cjs)
- **Prettier** - 代码格式化工具，配置文件为 [.prettierrc.cjs](mdc:.prettierrc.cjs)
- **StyleLint** - CSS/SCSS代码风格检查，配置文件为 [.stylelintrc.cjs](mdc:.stylelintrc.cjs)
- **EditorConfig** - 跨编辑器代码样式配置，文件为 [.editorconfig](mdc:.editorconfig)

## 提交规范

项目使用Conventional Commits规范进行Git提交，配置文件为 [.commitlintrc.cjs](mdc:.commitlintrc.cjs)。提交类型包括：

- `feat:` - 新功能
- `fix:` - 修复Bug
- `perf:` - 性能优化
- `style:` - 代码风格调整
- `refactor:` - 重构代码
- `docs:` - 文档修改
- `test:` - 添加测试
- `build:` - 构建配置调整
- `ci:` - CI配置调整
- `chore:` - 其他修改

## 代码自动导入

项目使用unplugin-auto-import插件自动导入Vue和uni-app的API，无需在每个组件中手动导入。相关全局类型定义已在 [.eslintrc-auto-import.json](mdc:.eslintrc-auto-import.json) 中注册。

## TypeScript

项目使用TypeScript开发，tsconfig配置文件为 [tsconfig.json](mdc:tsconfig.json)。

# 应用架构指南

## 总体架构

项目基于uni-app框架，使用Vue 3 + TypeScript + Pinia开发，采用了现代化的前端架构设计。

## 状态管理

项目使用Pinia进行状态管理，配合`pinia-plugin-persistedstate`插件实现状态持久化。状态管理文件存放在`src/store`目录下。

## 拦截器系统

项目实现了完整的拦截器系统，集中在`src/interceptors`目录下：

- [route.ts](mdc:src/interceptors/route.ts) - 路由拦截器，实现登录保护
- [request.ts](mdc:src/interceptors/request.ts) - 请求拦截器，处理请求头、基础URL等
- [prototype.ts](mdc:src/interceptors/prototype.ts) - 原型扩展拦截器

## API服务

API服务按模块划分，位于`src/service`目录，主要包括：

- `app` - 应用通用服务
- `login` - 登录相关服务
- `index` - 首页相关服务

## 路由系统

项目使用uni-app页面路由系统，主要通过以下方式实现：

1. `pages.json` - 基础路由配置
2. `@uni-helper/vite-plugin-uni-pages` - 自动生成路由配置
3. `@uni-helper/vite-plugin-uni-layouts` - 页面布局系统

## 组件设计

组件按照功能划分，存放在`src/components`目录下，遵循单一职责原则。

# 变更日志记录指南

## 变更记录要求

每次修改代码后，必须在项目根目录的`changeLog`文件夹中创建或更新当天的变更日志文件。变更日志有助于跟踪项目的历史变化，便于团队成员了解代码演进过程。

## 文件命名规则

变更日志文件必须按照日期命名，格式为：`changeLog年月日.md`，例如：`changeLog20240528.md`。

每天应该创建一个新的变更日志文件，文件放在changeLog这个文件夹中，所有当天的修改都记录在这个文件中。

## 记录格式

变更记录应遵循以下Markdown格式：

````markdown
# 变更日志 - 年月日

## [HH:mm:ss] [变更类型]: 简短描述

**作者**: 你的名字  
**文件**: 被修改的文件路径

### 变更描述

详细说明变更的原因和目的

### 变更详情

**原代码**:

```代码语言
// 这里是修改前的原始代码
```
````

**新代码**:

```代码语言
// 这里是修改后的新代码
```

````

## 使用时间

当需要在变更日志中记录具体时间时，必须使用`mcp_time-mcp_current_time`MCP工具获取准确的当前时间。如果该工具调用失败，直接在终端获取当前时间，我的是Windows电脑。

```javascript
// 示例代码：获取当前时间（中国时区）
const currentTime = callFunction('mcp_time-mcp_current_time', {
  format: 'YYYY-MM-DD HH:mm:ss',
  timezone: 'Asia/Shanghai'
});
````

> **注意**：所有时间记录必须使用中国时区（Asia/Shanghai），确保团队成员看到一致的时间戳。

## 变更类型

变更类型应与Git提交类型保持一致：

- `feat` - 新功能
- `fix` - 修复Bug
- `refactor` - 代码重构
- `style` - 代码风格修改
- `docs` - 文档更新
- `perf` - 性能优化
- `test` - 测试相关
- `chore` - 其他修改

## 变更记录示例

````markdown
# 变更日志 - 2024年5月28日

## [10:30:45] feat: 添加用户登录表单验证功能

**作者**: 张三  
**文件**: src/pages/login/login.vue

### 变更描述

添加表单验证功能，防止用户提交空白或格式不正确的登录信息

### 变更详情

**原代码**:

```javascript
<script setup lang="ts">
const formData = reactive({
  username: '',
  password: ''
})

const handleLogin = () => {
  loginService.login(formData)
}
</script>
```
````

**新代码**:

```javascript
<script setup lang="ts">
const formData = reactive({
  username: '',
  password: ''
})

const formRules = {
  username: [{ required: true, message: '用户名不能为空' }],
  password: [
    { required: true, message: '密码不能为空' },
    { min: 6, message: '密码长度不能少于6位' }
  ]
}

const handleLogin = () => {
  formRef.value.validate().then(valid => {
    if (valid) {
      loginService.login(formData)
    }
  })
}
</script>
```

```

## 注意事项

1. 变更记录应使用Markdown格式以增强可读性和结构化
2. 原代码和新代码应包含足够的上下文，确保变更易于理解
3. 如果修改涉及多个文件，应分别列出每个文件的变更
4. 对于大型重构，可以适当精简代码示例，但必须保留关键变更
5. 每天开始工作时检查是否需要创建新的日期命名的变更日志文件
6. 代码块应指定适当的语言（如javascript, typescript, vue等）以获得语法高亮
```

---

description:
globs:
alwaysApply: true

---

# UI框架指南

## 主要UI框架

项目主要使用**wot-design-uni**作为UI组件库，这是一个基于uni-app的移动端组件库，由京东设计开发。

```typescript
// 使用wot-design-uni组件示例
<wd-button type="primary">按钮</wd-button>
<wd-cell title="标题" value="内容"></wd-cell>
```

## 分页组件

项目使用**z-paging**作为分页和下拉刷新、上拉加载组件：

```vue
<z-paging ref="paging" v-model="dataList" @query="queryList">
  <!-- 内容，如cell组件等 -->
</z-paging>
```

## 样式系统

样式系统由多个部分组成：

1. **UnoCSS** - 原子化CSS框架，通过类名直接应用样式

   ```html
   <view class="flex items-center justify-between p-4"></view>
   ```

2. **SCSS** - 全局样式定义在`src/style`目录下

3. **uni.scss** - uni-app内置样式变量

## 图标系统

项目使用**Iconify**作为图标库，配置了Carbon图标集：

```
@iconify-json/carbon
```

## 适配策略

项目采用uni-app跨平台开发，支持：

- H5
- 微信小程序
- App
- 其他小程序平台

通过条件编译实现不同平台的适配：

```vue
<!-- #ifdef H5 -->
<view>H5 环境特有内容</view>
<!-- #endif -->

<!-- #ifdef MP-WEIXIN -->
<view>微信小程序特有内容</view>
<!-- #endif -->
```

## 通用页面模式

### 列表页面

列表页面通常包含以下几个部分：

1. **顶部搜索栏** - 使用`wd-input`组件，添加搜索图标
2. **内容列表** - 使用`z-paging`组件实现下拉刷新和上拉加载
3. **列表项** - 使用卡片式布局，包含标题、状态标签、详情信息和时间等
4. **底部按钮** - 固定在底部，使用`wd-button`组件，通常为全宽按钮

```vue
<!-- 列表页面示例 -->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <wd-input placeholder="搜索" prefix-icon="search" />
  </view>

  <!-- 列表内容 -->
  <z-paging ref="paging" @query="queryList">
    <view class="list-item" v-for="item in list">
      <!-- 列表项内容 -->
    </view>
  </z-paging>

  <!-- 底部按钮 -->
  <view class="bottom-button">
    <wd-button type="primary" block>新增</wd-button>
  </view>
</view>
```

### 表单页面

表单页面通常包含以下几个部分：

1. **表单项组** - 白色背景，每个表单项包含标签和输入控件
2. **提交按钮** - 固定在底部，使用`wd-button`组件，通常为全宽按钮
3. **图片上传区** - 使用九宫格布局，带有添加按钮和删除功能

```vue
<!-- 表单页面示例 -->
<view class="container">
  <form @submit="handleSubmit">
    <!-- 表单内容 -->
    <view class="form-content">
      <view class="form-item" v-for="item in formItems">
        <view class="form-label">{{ item.label }}</view>
        <view class="form-value">
          <!-- 输入控件 -->
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-button">
      <wd-button type="primary" block form-type="submit">提交</wd-button>
    </view>
  </form>
</view>
```

## 质量检验模块UI规范

质量检验模块遵循以下特定UI规范：

### 色彩规范

- **主色调**: 品质检验使用蓝色系列 (#2979ff) 作为模块主色
- **状态颜色**:
  - 合格: 绿色 (#19be6b)
  - 不合格: 红色 (#fa3534)
  - 待检验: 橙色 (#ff9900)

### 来料检验列表页

- 采用卡片式布局，突出显示材料名称和检验状态
- 使用标签显示检验状态，采用对应的状态颜色
- 列表项中显示关键信息：批次号、数量、不良品数量、检验人员
- 底部固定"新增来料检验"按钮

### 来料检验表单

- 表单项布局为上下结构：标签在上，输入控件在下
- 必填项标签带有红色星号标识
- 系统自动填充的字段使用灰色背景区分
- 图片上传区支持多图上传，采用九宫格布局
- 表单底部固定提交按钮

### 其他质量检验页面

委外加工检验和完工检验页面应保持与来料检验页面相同的UI风格和交互模式，确保整个质量检验模块的用户体验一致性。
