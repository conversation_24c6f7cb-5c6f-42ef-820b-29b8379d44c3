<route lang="json5" type="page">
{
  layout: 'default',
  needLogin: true,
  style: {
    navigationBarTitleText: '我的任务',
    backgroundColor: '#f3f4f6',
    enablePullDownRefresh: true,
  },
}
</route>

<template>
  <view class="my-task-page">
    <!-- 日期范围查询 -->
    <view class="search-card">
      <view class="search-header">
        <wd-icon name="calendar" size="20px" color="#667eea" />
        <text class="search-title">日期筛选</text>
      </view>
      <view class="search-content">
        <view class="date-range-container">
          <wd-calendar
            v-model="dateRange"
            type="daterange"
            label="请选择日期范围"
            allow-same-day
            @confirm="handleDateChange"
          />
        </view>
        <view class="quick-buttons">
          <wd-button size="small" @click="selectToday">今天</wd-button>
          <wd-button size="small" @click="selectWeek">本周</wd-button>
          <wd-button size="small" @tap="selectMonth">本月</wd-button>
        </view>
      </view>
    </view>

    <!-- 任务统计卡片 -->
    <view class="stats-card">
      <view class="stats-header">
        <wd-icon name="clock" size="24px" color="#667eea" />
        <text class="stats-title">任务统计</text>
      </view>
      <view class="stats-content">
        <view class="stats-item">
          <text class="stats-number">{{ myTask?.total || 0 }}</text>
          <text class="stats-label">总任务</text>
        </view>
        <view class="stats-divider"></view>
        <view class="stats-item">
          <text class="stats-number completed">{{ myTask?.completed || 0 }}</text>
          <text class="stats-label">已完成</text>
        </view>
        <view class="stats-divider"></view>
        <view class="stats-item">
          <text class="stats-number pending">
            {{ (myTask?.total || 0) - (myTask?.completed || 0) }}
          </text>
          <text class="stats-label">待处理</text>
        </view>
      </view>
    </view>

    <!-- 任务列表 -->
    <view class="task-list-container">
      <view class="list-header">
        <text class="list-title">任务列表</text>
        <view class="refresh-btn" @click="refreshTasks">
          <wd-icon name="refresh" size="16px" color="#666666" />
          <text class="refresh-text">刷新</text>
        </view>
      </view>

      <!-- 任务列表内容 -->
      <template v-if="taskList.length > 0">
        <view
          class="task-item"
          v-for="item in taskList"
          :key="item.id"
          :class="{ 'task-disabled': isTaskDisabled(item) }"
        >
          <!-- 设备信息 -->
          <view class="task-row" v-if="!!item.device">
            <text class="task-label">设备号：</text>
            <text class="task-value">{{ item.device.machineNumber }}</text>
          </view>

          <!-- 任务类型 -->
          <view class="task-row">
            <text class="task-label">任务类型：</text>
            <text class="task-value">{{ taskTitleMap[item.taskType] }}</text>
            <wd-tag :type="getTaskTypeColor(item.taskType)" size="small" plain>
              {{ taskTitleMap[item.taskType] }}
            </wd-tag>
          </view>

          <!-- 加工单 -->
          <view class="task-row">
            <text class="task-label">加工单：</text>
            <text class="task-value">{{ item.machineOrder.machineOrderNum }}</text>
          </view>

          <!-- 创建时间 -->
          <view class="task-row">
            <text class="task-label">创建时间：</text>
            <text class="task-value">{{ formatDateTime(item.createTime) }}</text>
          </view>

          <!-- 任务状态和操作按钮 -->
          <view class="task-row task-btn-row" v-if="item.taskType === 0 || item.taskType === 4">
            <view v-if="item.status === 0">
              <wd-button type="primary" size="small" @click="handleTask(item)">处理</wd-button>
            </view>
            <view v-if="item.status === 1" @click="jumpDetail(item)">
              <image class="status-img" :src="completeImage"></image>
            </view>
          </view>

          <view
            class="task-row task-btn-row"
            v-if="item.taskType === 1 || item.taskType === 2 || item.taskType === 3"
          >
            <view v-if="item.taskStatus === 0 && item.status == 0">
              <wd-button type="primary" size="small" @click="handleTask(item)">处理</wd-button>
            </view>
            <view v-else-if="item.taskStatus === 1">
              <image class="status-img" :src="expiredImage"></image>
            </view>
            <view v-else-if="item.taskStatus === 2">
              <image class="status-img" :src="exceptionImage"></image>
            </view>
            <view v-else-if="item.taskStatus === 0 && item.status === 1" @click="jumpDetail(item)">
              <image class="status-img" :src="completeImage"></image>
            </view>
          </view>
        </view>

        <!-- 加载更多组件 -->
        <wd-loadmore :state="loadMoreState" @loadmore="loadMore" />
      </template>

      <!-- 空状态 -->
      <template v-else>
        <view class="empty-state">
          <wd-img :src="noTaskImage" class="empty-img" />
          <text class="empty-text">暂无任务</text>
          <wd-button type="primary" size="small" @click="refreshTasks" class="empty-btn">
            刷新任务
          </wd-button>
        </view>
      </template>
    </view>

    <!-- 加载状态 -->
    <wd-loading v-if="loading" custom-class="loading-center" />
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { onReachBottom } from '@dcloudio/uni-app'
import { myTaskList, ITodayTaskRes, IMyTaskParams } from '@/service/arrangement/index'
import { compareWithToDay, formatDateToChinese } from '@/utils/time'
import { LoadMoreState } from 'wot-design-uni/components/wd-loadmore/types'
import completeImage from '@/static/images/index/complete.png'
import expiredImage from '@/static/images/index/expired.png'
import exceptionImage from '@/static/images/index/exception.png'
import noTaskImage from '@/static/images/index/noTask.png'

// 响应式数据
const loading = ref(false)
const myTask = ref<ITodayTaskRes>()
const taskList = ref([])

// 分页相关
let currentPage = 0
const pageSize = 10
let totalPage = 1
const loadMoreState = ref<LoadMoreState>('loading')

// 日期范围查询
const dateRange = ref<number[]>([])

// 任务类型映射
const taskTitleMap = {
  0: '排班',
  1: '领料',
  2: '调机',
  3: '报工',
  4: '质检',
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return ''
  const date = new Date(dateTime)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// 格式化时间戳为日期字符串
const formatTimestampToDate = (timestamp: number) => {
  const date = new Date(timestamp)
  return date.toISOString().split('T')[0]
}

// 构建查询参数
const buildQueryParams = (page: number = 1, size: number = 10) => {
  const params: IMyTaskParams = {
    page,
    size,
  }

  if (dateRange.value && dateRange.value.length === 2) {
    params.startTime = formatTimestampToDate(dateRange.value[0]) + ' 00:00:00'
    params.endTime = formatTimestampToDate(dateRange.value[1]) + ' 23:59:59'
  }

  return params
}

// 查询任务列表
const queryTaskList = async (page: number, isRefresh: boolean = false) => {
  try {
    loading.value = true
    const params = buildQueryParams(page, pageSize)
    const res = await myTaskList(params)
    console.log('我的任务', res)

    // 更新统计信息
    myTask.value = res.data

    // 处理分页数据
    if (isRefresh) {
      // 刷新时重置列表
      taskList.value = res.data.content
    } else {
      // 加载更多时追加数据
      taskList.value = [...taskList.value, ...res.data.content]
    }

    // 计算总页数
    const totalElements = res.data.total || 0
    totalPage = Math.ceil(totalElements / pageSize)

    // 更新加载状态
    if (page >= totalPage - 1) {
      loadMoreState.value = 'finished'
    } else {
      loadMoreState.value = 'loading'
    }
  } catch (error) {
    console.error('获取我的任务失败:', error)
    loadMoreState.value = 'error'
    uni.showToast({
      title: '获取任务失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 获取我的任务列表（刷新）
const getMyTaskList = async () => {
  currentPage = 0
  await queryTaskList(currentPage++, true)
}

// 刷新任务
const refreshTasks = () => {
  getMyTaskList()
}

// 加载更多
const loadMore = async () => {
  if (currentPage < totalPage - 1) {
    await queryTaskList(currentPage++, false)
  }
}

// 日期范围改变处理
const handleDateChange = ({ value }: { value: number[] }) => {
  dateRange.value = value
  // 重置分页状态
  loadMoreState.value = 'loading'
  getMyTaskList()
}

// 选择今天
const selectToday = () => {
  const today = new Date()
  const todayTimestamp = today.getTime()
  dateRange.value = [todayTimestamp, todayTimestamp]
  // 重置分页状态
  loadMoreState.value = 'loading'
  getMyTaskList()
}

// 选择本周
const selectWeek = () => {
  const today = new Date()
  const dayOfWeek = today.getDay()
  const startOfWeek = new Date(today)
  startOfWeek.setDate(today.getDate() - dayOfWeek + 1) // 周一
  const endOfWeek = new Date(today)
  endOfWeek.setDate(today.getDate() - dayOfWeek + 7) // 周日

  dateRange.value = [startOfWeek.getTime(), endOfWeek.getTime()]
  // 重置分页状态
  loadMoreState.value = 'loading'
  getMyTaskList()
}

// 选择本月
const selectMonth = () => {
  const today = new Date()
  const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)
  const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0)

  dateRange.value = [startOfMonth.getTime(), endOfMonth.getTime()]
  // 重置分页状态
  loadMoreState.value = 'loading'
  getMyTaskList()
}

// 判断任务是否禁用
const isTaskDisabled = (item: any) => {
  return !(item.taskType === 3 && item.status === 0 && item.taskStatus === 0 && item.workReportFlag)
}

// 获取任务类型颜色
const getTaskTypeColor = (taskType: number) => {
  const colorMap = {
    0: 'primary', // 排班
    1: 'success', // 领料
    2: 'warning', // 调机
    3: 'info', // 报工
    4: 'danger', // 质检
  }
  return colorMap[taskType] || 'primary'
}

// 处理任务按钮事件
const handleTask = (item: any) => {
  const flag = compareWithToDay(item.scheduleDate)
  if (flag === 1) {
    const content =
      '该任务是' +
      formatDateToChinese(item.scheduleDate) +
      '的' +
      item.shift +
      '任务，请确认是否提前执行？'
    uni.showModal({
      title: '提示',
      content,
      confirmText: '确定',
      cancelText: '取消',
      success: function (res) {
        if (res.confirm) {
          jumpDetail(item)
        }
      },
    })
  } else {
    jumpDetail(item)
  }
}

// 跳转到任务详情页面
const jumpDetail = (item: any) => {
  if (item.taskType === 0) {
    const scheduleData = JSON.stringify(item.machineOrder)
    uni.navigateTo({
      url: '/pages/arrangement/tasks?scheduleData=' + scheduleData,
    })
  } else if (item.taskType === 1) {
    const pickData = JSON.stringify(item.machineOrder)
    uni.navigateTo({
      url: '/pages/task/picking/picking?pickData=' + pickData,
    })
  } else if (item.taskType === 2) {
    const deviceData = {
      machineOrder: JSON.parse(JSON.stringify(item.machineOrder)),
      device: {
        machineTaskId: item.machineTaskId,
        machineNumber: item.device.machineNumber,
      },
    }
    try {
      uni.removeStorageSync('deviceRun')
    } catch (e) {
    } finally {
      uni.navigateTo({
        url: '/pages/task/deviceRun/index?pickData=' + JSON.stringify(deviceData),
      })
    }
  } else if (item.taskType === 3) {
    uni.navigateTo({
      url: '/pages/report/work?taskId=' + item.taskId,
    })
  } else if (item.taskType === 4) {
    const qualityData = {
      id: item.id,
      machineNumber: item.device.machineNumber,
      reportId: item.reportId,
    }
    uni.navigateTo({
      url: '/pages/quality/completed-inspection/form?qualityData=' + JSON.stringify(qualityData),
    })
  }
}

// 触底加载更多
onReachBottom(() => {
  if (loadMoreState.value === 'loading' && currentPage < totalPage - 1) {
    loadMore()
  }
})

// 下拉刷新
onPullDownRefresh(() => {
  getMyTaskList().finally(() => {
    uni.stopPullDownRefresh()
  })
})

// 页面加载时获取数据
onMounted(() => {
  // 默认选择今天
  const today = new Date()
  const todayTimestamp = today.getTime()
  dateRange.value = [todayTimestamp, todayTimestamp]

  // 获取任务列表
  getMyTaskList()
})
</script>

<style lang="scss" scoped>
.my-task-page {
  min-height: 100vh;
  padding: 16px;
  background-color: #f3f4f6;
}

/* 搜索卡片样式 */
.search-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.search-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.search-title {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  margin-left: 6px;
}

.search-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.date-range-container {
  flex: 1;
}

.quick-buttons {
  display: flex;
  gap: 8px;
  justify-content: flex-start;
}

/* 统计卡片样式 */
.stats-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.stats-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.stats-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  margin-left: 8px;
}

.stats-content {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stats-number {
  font-size: 24px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 4px;

  &.completed {
    color: #19be6b;
  }

  &.pending {
    color: #ff9900;
  }
}

.stats-label {
  font-size: 12px;
  color: #666666;
}

.stats-divider {
  width: 1px;
  height: 40px;
  background-color: #f0f0f0;
  margin: 0 16px;
}

/* 任务列表容器 */
.task-list-container {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.list-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
}

.refresh-btn {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: #f8f9fa;
}

.refresh-text {
  font-size: 12px;
  color: #666666;
  margin-left: 4px;
}

/* 任务项样式 */
.task-item {
  padding: 16px;
  margin-bottom: 12px;
  background: #f7fafd;
  border-radius: 8px;
  border-left: 4px solid #667eea;
  box-shadow: 0 1px 4px rgba(41, 121, 255, 0.04);

  &.task-disabled {
    opacity: 0.6;
    border-left-color: #cccccc;
  }
}

.task-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }
}

.task-label {
  min-width: 80px;
  font-size: 13px;
  color: #888888;
}

.task-value {
  margin-left: 8px;
  font-size: 14px;
  color: #333333;
  flex: 1;
}

.task-btn-row {
  justify-content: flex-end;
  margin-top: 8px;
}

.status-img {
  width: 40px;
  height: 40px;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px 0;
}

.empty-img {
  width: 120px;
  height: 120px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 14px;
  color: #999999;
  margin-bottom: 16px;
}

.empty-btn {
  margin-top: 8px;
}

/* 加载状态 */
.loading-center {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}
</style>
