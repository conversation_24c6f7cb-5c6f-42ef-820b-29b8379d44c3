<template>
  <wd-card class="info-card" :title="title" :shadow="shadow">
    <view class="info-card-content">
      <slot>
        <!-- 默认渲染传入的字段信息 -->
        <view v-for="(item, index) in items" :key="index" class="info-item">
          <text class="info-label">{{ item.label }}</text>
          <text class="info-value">{{ item.value }}</text>
        </view>
      </slot>
    </view>
    <template #footer v-if="$slots.footer">
      <slot name="footer"></slot>
    </template>
  </wd-card>
</template>

<script lang="ts" setup>
import { defineProps } from 'vue'

const props = defineProps({
  // 卡片标题
  title: {
    type: String,
    default: '',
  },
  // 阴影效果：always/hover/none
  shadow: {
    type: String,
    default: 'always',
  },
  // 要显示的信息项数组
  items: {
    type: Array,
    default: () => [],
  },
})
</script>

<style lang="scss" scoped>
.info-card {
  margin-bottom: 20rpx;
  overflow: hidden;
  border-radius: 12rpx;
}

.info-card-content {
  padding: 0 4rpx;
}

.info-item {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  margin-bottom: 16rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.info-label {
  min-width: 160rpx;
  margin-right: 16rpx;
  font-size: 28rpx;
  color: #666;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  word-break: break-all;
}

@media screen and (max-width: 375px) {
  .info-label {
    min-width: 140rpx;
  }
}
</style>
