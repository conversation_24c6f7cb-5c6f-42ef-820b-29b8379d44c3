---
description: 
globs: 
alwaysApply: true
---
# 项目结构指南

## 目录结构

这个移动应用基于uni-app框架开发，使用TypeScript + Vue 3 + Vite构建。主要目录结构如下：

- `src/` - 源代码目录
  - `components/` - 可复用的组件
  - `hooks/` - 自定义钩子函数
  - `interceptors/` - 拦截器（请求、路由等）
  - `layouts/` - 页面布局模板
  - `pages/` - 主包页面
  - `pages-sub/` - 分包页面
  - `service/` - API服务
  - `static/` - 静态资源
  - `store/` - Pinia状态管理
  - `style/` - 全局样式
  - `types/` - TypeScript类型定义
  - `utils/` - 工具函数
- `env/` - 环境变量配置
- `vite-plugins/` - 自定义Vite插件

## 主要入口文件

- [main.ts](mdc:src/main.ts) - 应用入口文件
- [App.vue](mdc:src/App.vue) - 根组件
- [manifest.json](mdc:src/manifest.json) - uni-app配置文件
- [pages.json](mdc:src/pages.json) - 页面路由配置

## 构建工具

项目使用Vite作为构建工具，配置文件为[vite.config.ts](mdc:vite.config.ts)，支持多平台构建（H5、微信小程序、App等）。

