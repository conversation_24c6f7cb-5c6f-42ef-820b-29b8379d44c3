import '@/style/index.scss'
import { VueQueryPlugin } from '@tanstack/vue-query'
import 'virtual:uno.css'
import { createSSRApp } from 'vue'

import App from './App.vue'
import { prototypeInterceptor, requestInterceptor, routeInterceptor } from './interceptors'
import store from './store'
import LEchart from '@/uni_modules/lime-echart/components/l-echart/l-echart.vue'
import LimeEchart from '@/uni_modules/lime-echart/components/lime-echart/index.vue'

export function createApp() {
  const app = createSSRApp(App)
  app.use(store)
  app.use(routeInterceptor)
  app.use(requestInterceptor)
  app.use(prototypeInterceptor)
  app.use(VueQueryPlugin)

  // 全局注册echarts组件
  app.component('l-echart', LEchart)
  app.component('lime-echart', LimeEchart)

  return {
    app,
  }
}
