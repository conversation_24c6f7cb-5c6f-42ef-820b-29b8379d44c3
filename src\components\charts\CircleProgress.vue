<template>
  <view class="circle-progress" :style="{ width: size + 'px', height: size + 'px' }">
    <view class="circle-progress-wrapper">
      <!-- 背景圆环 -->
      <view
        class="circle-progress-bg"
        :style="{
          borderWidth: strokeWidth + 'px',
          borderColor: trailColor,
        }"
      ></view>

      <!-- 左半部分进度 -->
      <view
        class="circle-progress-left"
        :style="{
          borderWidth: strokeWidth + 'px',
          borderColor: strokeColor,
          transform: `rotate(${leftDeg}deg)`,
        }"
      ></view>

      <!-- 右半部分进度 -->
      <view
        class="circle-progress-right"
        :style="{
          borderWidth: strokeWidth + 'px',
          borderColor: strokeColor,
          transform: `rotate(${rightDeg}deg)`,
        }"
      ></view>

      <!-- 内容插槽 -->
      <view class="circle-progress-content">
        <slot>{{ current }}%</slot>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch } from 'vue'

export default defineComponent({
  name: 'CircleProgress',
  props: {
    // 目标百分比
    percent: {
      type: Number,
      default: 0,
    },
    // 当前百分比，支持v-model绑定
    current: {
      type: Number,
      default: 0,
    },
    // 圆环大小
    size: {
      type: Number,
      default: 120,
    },
    // 圆环宽度
    strokeWidth: {
      type: Number,
      default: 6,
    },
    // 圆环颜色
    strokeColor: {
      type: String,
      default: '#2979ff',
    },
    // 背景圆环宽度
    trailWidth: {
      type: Number,
      default: 6,
    },
    // 背景圆环颜色
    trailColor: {
      type: String,
      default: '#eaeef2',
    },
    // 是否为仪表盘样式
    dashboard: {
      type: Boolean,
      default: false,
    },
    // 动画持续时间，单位毫秒
    duration: {
      type: Number,
      default: 1000,
    },
    // 圆环总长度
    max: {
      type: Number,
      default: 100,
    },
    // 是否顺时针增加进度
    clockwise: {
      type: Boolean,
      default: true,
    },
  },
  emits: ['update:current'],
  setup(props, { emit }) {
    // 计算左右两侧旋转角度
    const leftDeg = computed(() => {
      const percentage = (props.current / props.max) * 100
      if (percentage <= 50) {
        return 180 // 左侧不旋转
      } else {
        return 180 + ((percentage - 50) / 50) * 180 // 左侧开始旋转
      }
    })

    const rightDeg = computed(() => {
      const percentage = (props.current / props.max) * 100
      if (percentage <= 50) {
        return (percentage / 50) * 180 // 右侧旋转
      } else {
        return 180 // 右侧旋转到最大位置
      }
    })

    // 动画函数
    const animateProgress = (targetValue) => {
      // 确保目标值在合理范围内
      targetValue = Math.max(0, Math.min(props.max, targetValue))

      const startValue = props.current
      const difference = targetValue - startValue
      const startTime = Date.now()
      const endTime = startTime + props.duration

      // 如果没有变化，直接返回
      if (difference === 0) return

      const animate = () => {
        const now = Date.now()
        const remainingTime = Math.max(0, endTime - now)
        const remainingPercentage = remainingTime / props.duration

        // 计算当前进度值
        let currentValue
        if (difference > 0) {
          currentValue = targetValue - difference * remainingPercentage
        } else {
          currentValue = startValue - Math.abs(difference) * (1 - remainingPercentage)
        }

        currentValue = Math.round(currentValue)

        // 更新current值
        emit('update:current', currentValue)

        if (remainingTime > 0) {
          requestAnimationFrame(animate)
        } else {
          emit('update:current', targetValue)
        }
      }

      requestAnimationFrame(animate)
    }

    // 监听percent变化，动画过渡到目标值
    watch(
      () => props.percent,
      (newVal) => {
        animateProgress(newVal)
      },
      { immediate: true },
    )

    return {
      leftDeg,
      rightDeg,
    }
  },
})
</script>

<style scoped>
.circle-progress {
  position: relative;
  display: inline-block;
}

.circle-progress-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.circle-progress-bg,
.circle-progress-left,
.circle-progress-right {
  position: absolute;
  top: 0;
  left: 0;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  border-style: solid;
  border-radius: 50%;
}

.circle-progress-bg {
  border-color: v-bind(trailColor);
}

.circle-progress-left,
.circle-progress-right {
  clip: rect(0, v-bind(size + 'px'), v-bind(size + 'px'), calc(v-bind(size) / 2 + 'px'));
  border-color: transparent;
}

.circle-progress-right {
  clip: rect(0, calc(v-bind(size) / 2 + 'px'), v-bind(size + 'px'), 0);
}

.circle-progress-content {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  font-size: calc(v-bind(size) / 6);
  color: v-bind(strokeColor);
}
</style>
