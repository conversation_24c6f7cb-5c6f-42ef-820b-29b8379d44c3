<route lang="json5">
{
  needLogin: true,
  style: {
    navigationBarTitleText: '压铸调机管理',
  },
}
</route>

<template>
  <view class="container">
    <!-- 扫描机台号 -->
    <view class="scan-section">
      <view class="scan-section-inp">
        <wd-input v-model="machineCode" placeholder="扫描机台号" clearable>
          <template #suffix>
            <wd-icon name="scan" @tap.stop="scanMachineCode" />
          </template>
        </wd-input>
      </view>
    </view>

    <!-- 当前工单信息 - 只有在扫描后显示 -->
    <view v-if="showWorkOrderInfo" class="info-section">
      <view class="section-title">
        <view class="title-bar"></view>
        <text class="title-text">当前工单信息</text>
      </view>

      <view class="info-item">
        <text class="info-label">生产机台:</text>
        <text class="info-value">{{ machineTaskInfo.machineNumber }}</text>
      </view>

      <view class="info-item">
        <text class="info-label">排程时间:</text>
        <text class="info-value">{{ machineTaskInfo.scheduleDate }}</text>
      </view>

      <view class="info-item">
        <text class="info-label">配件名称:</text>
        <text class="info-value">{{ machineTaskInfo.partsName }}</text>
      </view>

      <view class="info-item">
        <text class="info-label">工序名称:</text>
        <text class="info-value">{{ machineTaskInfo.procedureName }}</text>
      </view>

      <view class="info-item">
        <text class="info-label">计划数量:</text>
        <text class="info-value">{{ machineTaskInfo.planQuantity }}件</text>
      </view>

      <view class="info-item">
        <text class="info-label">完成数量:</text>
        <text class="info-value">{{ machineTaskInfo.completedQuantity }}件</text>
      </view>

      <view class="info-item">
        <text class="info-label">备注:</text>
        <text class="info-value">{{ machineTaskInfo.remark }}</text>
      </view>
    </view>

    <!-- 已绑定信息 - 只有在扫描后显示 -->
    <view v-if="showWorkOrderInfo" class="form-section">
      <view class="form-item">
        <text class="form-label">已绑定信息:</text>
        <text class="form-value">{{ boundInfo }}</text>
      </view>

      <!-- 根据机台类型显示不同的扫描项 -->
      <view class="form-item">
        <text class="form-label">扫描模具条码:</text>
        <view class="form-input-wrapper">
          <wd-input v-model="toolCode" placeholder="请输入模具条码" clearable>
            <template #suffix>
              <wd-icon name="scan" @tap.stop="scanToolCode" />
            </template>
          </wd-input>
        </view>
      </view>
    </view>

    <!-- 产品信息和工艺卡图片 - 只有在扫描后显示 -->
    <view v-if="showProductInfo" class="info-section">
      <view class="section-title">
        <view class="title-bar"></view>
        <text class="title-text">产品信息</text>
      </view>

      <view class="info-item">
        <text class="info-label">产品名称:</text>
        <text class="info-value">{{ materialName }}</text>
      </view>

      <view class="info-item">
        <text class="info-label">产品代码:</text>
        <text class="info-value">{{ code }}</text>
      </view>

      <view class="info-item" v-if="cardImg">
        <text class="info-label">工艺卡图片:</text>
        <view class="info-value">
          <image
            :src="cardImg"
            mode="aspectFit"
            class="card-img"
            @click="previewImage(cardImg)"
          ></image>
        </view>
      </view>
    </view>

    <!-- 底部按钮 - 只有在扫描后显示 -->
    <view v-if="showWorkOrderInfo" class="submit-button">
      <wd-button type="primary" block @click="handleSubmit">确认</wd-button>
    </view>

    <!-- 弹窗 提示有加工计划和加工单未完成，是否继续绑定,给出确认和取消按钮 -->
    <wd-popup v-model="showBindPopup" position="center" :close-on-click-modal="false">
      <view class="custom-popup">
        <view class="popup-header">
          <wd-icon name="warning" color="#fa3534" size="32" />
          <text class="popup-title">提示</text>
        </view>
        <view class="popup-content">当前设备有加工计划和加工单未完成，是否继续绑定？</view>
        <view class="popup-actions">
          <wd-button type="warning" class="popup-btn" @click="handleCancel">取消</wd-button>
          <wd-button type="primary" class="popup-btn" @click="handleConfirm">确认</wd-button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  getPressureMachineTaskInfo,
  getPressurePartsId,
  postPressureParts,
} from '@/service/adjustment'
import { getFileProxyPrefix } from '@/utils/config'
import { IMachineLatestTask } from '@/service/adjustment/types'

// 控制工单信息和绑定类型信息的显示
const showWorkOrderInfo = ref(false)

// 机台码
const machineCode = ref('')
// 工具码（夹具或模具）
const toolCode = ref('')
// 已绑定信息
const boundInfo = ref('')

const machineTaskInfo = ref<IMachineLatestTask>()

const partsId = ref()
const deviceId = ref()

// 产品信息
const materialName = ref('')
const code = ref('')
const cardImg = ref('')

// 控制产品信息和工艺卡图片的显示
const showProductInfo = ref(false)

// 扫描机台码
const scanMachineCode = async () => {
  console.log('扫描机台码')

  const response = await getPressureMachineTaskInfo({
    deviceNum: machineCode.value,
  })

  machineTaskInfo.value = response.data
  deviceId.value = machineTaskInfo.value.id
  // 模拟API获取工单信息和绑定信息的延迟
  uni.showLoading({
    title: '获取信息中...',
  })

  setTimeout(() => {
    // 显示工单信息和绑定信息
    showWorkOrderInfo.value = true

    uni.hideLoading()
  }, 500)
}

// 扫描工具码（夹具或模具）

const scanToolCode = async () => {
  const response = await getPressurePartsId({
    mouldNo: toolCode.value,
  })

  partsId.value = response.data.mouldId
  materialName.value = response.data.product.materialName
  code.value = response.data.product.code
  cardImg.value = getFileProxyPrefix() + response.data.cardImg
  showProductInfo.value = true
}

// 弹窗 提示有加工计划和加工单未完成，是否继续绑定
const showBindPopup = ref(false)

// 取消绑定
const handleCancel = () => {
  showBindPopup.value = false
}

// 预览图片
const previewImage = (currentImg: string) => {
  uni.previewImage({
    urls: [currentImg],
    current: currentImg,
  })
}

// 确认绑定
const handleConfirm = async () => {
  showBindPopup.value = false
  const resp = await postPressureParts({
    deviceId: deviceId.value,
    partsId: partsId.value,
    creatorId: 1,
  })

  // 模拟提交成功
  uni.showLoading({
    title: '提交中...',
  })

  setTimeout(() => {
    uni.hideLoading()
    uni.showToast({
      title: '调机成功',
      icon: 'success',
      duration: 2000,
      success: () => {
        setTimeout(() => {
          // 返回上一页
          uni.navigateTo({
            url: '/pages/machine/casting/index',
          })
        }, 2000)
      },
    })
  }, 1500)
}

// 提交表单
const handleSubmit = async () => {
  if (!machineCode.value) {
    uni.showToast({
      title: '请输入模具条码',
      icon: 'none',
    })
    return
  }

  if (!toolCode.value) {
    uni.showToast({
      title: '请输入模具条码',
      icon: 'none',
    })
    return
  }

  // 如果加工计划（1：进行中）和加工单（false：进行中），则弹窗提示
  if (
    machineTaskInfo.value.order_complete_status === false ||
    machineTaskInfo.value.plan_complete_status === 1
  ) {
    showBindPopup.value = true
    return
  }

  const resp = await postPressureParts({
    deviceId: deviceId.value,
    partsId: partsId.value,
    creatorId: 1,
  })

  // 模拟提交成功
  uni.showLoading({
    title: '提交中...',
  })

  setTimeout(() => {
    uni.hideLoading()
    uni.showToast({
      title: '调机成功',
      icon: 'success',
      duration: 2000,
      success: () => {
        setTimeout(() => {
          // 返回上一页
          uni.navigateTo({
            url: '/pages/machine/casting/index',
          })
        }, 2000)
      },
    })
  }, 1500)
}
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding-bottom: 120rpx;
  background-color: #f5f5f5;
}

.scan-section {
  width: 100%;
  background-color: #ffffff;
}

.scan-section-inp {
  width: 80%;
  padding: 10rpx;
  margin: 0 auto;
}

.info-section {
  width: 100%;
  padding: 30rpx;
  margin-top: 20rpx;
  background-color: #ffffff;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.title-bar {
  width: 8rpx;
  height: 36rpx;
  margin-right: 20rpx;
  background-color: #2979ff;
  border-radius: 4rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.info-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #666666;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
  word-break: break-all;
}

.form-section {
  width: 100%;
  padding: 30rpx;
  margin-top: 20rpx;
  background-color: #ffffff;
}

.form-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 30rpx;
}

.form-label {
  margin-bottom: 16rpx;
  font-size: 28rpx;
  color: #666666;
}

.form-value {
  font-size: 28rpx;
  color: #333333;
  word-break: break-all;
}

.form-input-wrapper {
  width: 100%;
}

.submit-button {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 10;
  width: 100%;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-top: 1px solid #ebedf0;
}
/* 弹窗样式 */
.custom-popup {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 520rpx;
  padding: 48rpx 32rpx 32rpx 32rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(41, 121, 255, 0.08);
}
.popup-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.popup-title {
  margin-left: 16rpx;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}
.popup-content {
  margin-bottom: 40rpx;
  font-size: 30rpx;
  line-height: 1.6;
  color: #666;
  text-align: center;
}
.popup-actions {
  display: flex;
  gap: 24rpx;
  justify-content: space-between;
  width: 100%;
}
.popup-btn {
  flex: 1;
}

.card-img {
  width: 200rpx;
  height: 200rpx;
  overflow: hidden;
  border-radius: 8rpx;
}
</style>
