// 工单查询传参
export interface IMachineOrder {
  startTime: string
  endTime: string
  status: number
}
// 工单查询返回值
export interface IMachineOrderRes {
  id: number
  machineOrderNum: string
  shift: string
  scheduleDate: string
}

export interface IScheduleRes {
  id: number
  scheduleExist: boolean
  scheduleId: number
  userId: number
  personType: number
  status: number
  taskStatus: number
  nickName: string
  deviceStatus: number
  machineNum: string
  machinePlanNum: string
  materialName: string
  procedureName: string
  planQuantity: number
  taskMaterialCode: string
  remark: string
}

export interface IScheduleListRes {
  status: number
  scheduleList: IScheduleRes[]
}

export interface IScheduleCheckParams {
  machineTaskId: number
}

export interface IScheduleAddParams {
  machineTaskId: number
  userId: number
  personType: number
  workName: string
}

export interface IScheduleEditParams extends IScheduleAddParams {
  id: number
}

export interface IAbnormalParams {
  id: number
  remark: string | number
}

export interface IUnabnormalParams {
  id: number
}
