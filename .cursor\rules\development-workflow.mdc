---
description: 
globs: 
alwaysApply: true
---
# 开发工作流指南

## 这是前端项目
1. 编码时需要的数据都可以设置假数据，因为这是一个前端项目，后端还没有开发，所以也没有对接后端
## 开发环境准备

1. Node.js 18+
2. pnpm 7.30+ (项目使用pnpm管理依赖)

## 开发流程

1. 选择合适的开发命令启动开发服务：
   - H5: `pnpm dev`或`pnpm dev:h5`
   - 微信小程序: `pnpm dev:mp-weixin`
   - App: `pnpm dev:app`

## 目录结构规范

- 页面组件放在`src/pages`目录
- 可复用组件放在`src/components`目录
- API服务放在`src/service`目录，按功能模块划分
- 状态管理放在`src/store`目录
- 工具函数放在`src/utils`目录

## Git提交规范

项目使用Conventional Commits规范进行Git提交，提交前会自动进行代码检查：

1. 使用`pnpm cz`命令代替`git commit`进行规范化提交
2. 提交前会自动运行lint-staged进行代码检查和格式化
3. 提交信息必须符合commitlint规范

参考提交类型：

```
feat: 新功能
fix: 修复Bug
docs: 文档更新
style: 代码风格修改
refactor: 代码重构
perf: 性能优化
test: 添加测试
build: 构建相关
ci: CI配置相关
chore: 其他修改
```

