<route>
    {
      "name": "pressurePickList",
      "style": {
        "navigationBarTitleText": "压铸领料单"
      }
    }
</route>

<template>
  <view class="pressure-pick-list">
    <!-- 列表内容 -->
    <view class="list-section">
      <wd-cell-group v-for="item in list" :key="item.id">
        <wd-card title="压铸领料单记录" desc="领料单编号：{{ item.id }}" type="rectangle">
          <wd-cell title="批次码" :value="item.batchNo" />
          <wd-cell title="原料名称" :value="item.materialName" />
          <wd-cell title="数量" :value="item.pickNum" />
          <wd-cell title="领取时间" :value="item.createTime" />
        </wd-card>
      </wd-cell-group>
      <wd-loadmore :state="loadMoreState" @reload="loadMore" />
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-button">
      <wd-button type="primary" block @click="goToForm">开始领料</wd-button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { IPickListItem } from '@/service/pick/types'
import { getPickMaterialList } from '@/service/pick'
import { onLoad, onReachBottom } from '@dcloudio/uni-app'
import { LoadMoreState } from 'wot-design-uni/components/wd-loadmore/types'

// 分页相关参数
const pageSize = 10
let currentPage = 0
let totalPage = 10
const loadMoreState = ref<LoadMoreState>('loading')

// 添加loading状态
const loading = ref(false)

const list = ref<IPickListItem[]>([])

const loadData = () => {
  loading.value = true
  try {
    getPickMaterialList({
      category: 'pressure',
      page: currentPage++,
      size: pageSize, // 参数名应该改为size
    })
      .then((res) => {
        const newData = res.data.content
        list.value = [...list.value, ...newData]
        totalPage = Math.ceil(res.data.totalElements / pageSize)
        if (currentPage === totalPage) {
          loadMoreState.value = 'finished'
        }
      })
      .catch((err) => {
        console.log(err)
        loadMoreState.value = 'error'
      })
  } catch (error) {
    loadMoreState.value = 'error'
  } finally {
    loading.value = false
  }
}

// 加载更多
const loadMore = () => {
  loadData()
}

onReachBottom(() => {
  if (currentPage < totalPage) {
    loadMore()
  } else {
    loadMoreState.value = 'finished'
  }
})

onLoad(() => {
  loadData()
})

const goToForm = () => {
  uni.navigateTo({
    url: '/pages/pick/pressure/form',
  })
}
</script>

<style lang="scss" scoped>
.pressure-pick-list {
  min-height: 100vh;
  padding-bottom: 120rpx;
  background-color: var(--wot-bg-color);
}

.search-section {
  margin-top: 20rpx;

  .date-range {
    font-size: 28rpx;
    color: var(--wot-color-text-secondary);
  }
}

.list-section {
  margin-top: 20rpx;

  :deep(.wd-cell-group) {
    margin-bottom: 20rpx;
  }
}

.bottom-button {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 24rpx 32rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.content,
.title {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.content {
  justify-content: flex-start;
}
.title {
  justify-content: space-between;
}
.title-tip {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.25);
}
</style>
