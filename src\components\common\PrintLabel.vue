<template>
  <wd-popup
    v-model="show"
    position="center"
    custom-style="width: 90%; max-width: 500px; padding: 15px;"
  >
    <view class="print-label-container">
      <view class="print-label-header">
        <text class="title">标签信息</text>
        <wd-icon name="close" @click="handleCancel" class="close-icon" />
      </view>
      <!-- 使用表格布局展示标签信息，二维码独占3行，信息部分两列3行 -->
      <view class="print-label-content">
        <table class="label-table">
          <!-- 第一行：二维码和物料名 -->
          <tr>
            <td rowspan="3" class="qr-code-cell">
              <image :src="qrCodeUrl" mode="scaleToFill" class="qr-code-img" />
            </td>
            <td class="info-label">物料名:</td>
            <td class="info-value">{{ materialName }}</td>
          </tr>
          <!-- 第二行：时间信息 -->
          <tr>
            <td class="info-label">时间:</td>
            <td class="info-value">{{ time }}</td>
          </tr>
          <!-- 第三行：追溯码信息 -->
          <tr>
            <td class="info-label">追溯码:</td>
            <td class="info-value">{{ traceCode }}</td>
          </tr>
        </table>
      </view>
      <view class="print-label-footer">
        <wd-button @click="handleCancel" class="footer-btn">取消</wd-button>
        <wd-button type="primary" @click="handlePrint" class="footer-btn">打印</wd-button>
      </view>
    </view>
  </wd-popup>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import QRCode from 'qrcode'

const props = defineProps<{
  show: boolean
  materialName: string
  time: string
  traceCode: string
}>()

const emit = defineEmits(['update:show', 'print', 'cancel'])

const show = ref(props.show)

watch(
  () => props.show,
  (val) => {
    show.value = val
  },
)

watch(
  () => show.value,
  (val) => {
    emit('update:show', val)
  },
)

const qrCodeUrl = ref('')

const generateQRCode = async (text: string) => {
  try {
    qrCodeUrl.value = await QRCode.toDataURL(text, { errorCorrectionLevel: 'H', width: 200 })
  } catch (err) {
    console.error(err)
  }
}

watch(
  () => props.traceCode,
  (val) => {
    if (val) {
      generateQRCode(val)
    }
  },
  { immediate: true },
)

const handlePrint = () => {
  emit('print')
}

const handleCancel = () => {
  emit('cancel')
  show.value = false
}
</script>

<style lang="scss" scoped>
.print-label-container {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.print-label-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;

  .title {
    font-size: 18px;
    font-weight: bold;
  }

  .close-icon {
    font-size: 20px;
    color: #999;
  }
}

/* 表格布局样式 - 二维码独占3行，信息部分两列3行 */
.print-label-content {
  padding: 20px;
}

.label-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
  border: solid 1px #000;
}

.qr-code-cell {
  width: 40%;
  text-align: center;
  vertical-align: middle;
  padding: 10px;
  border: 1px solid #eee;

  .qr-code-img {
    width: 150px;
    height: 150px;
  }
}

.info-label {
  width: 25%;
  padding: 10px 15px;
  font-size: 14px;
  color: #666;
  background-color: #f8f9fa;
  border: 1px solid #eee;
  text-align: right;
  vertical-align: middle;
}

.info-value {
  width: 35%;
  padding: 10px 15px;
  font-size: 16px;
  font-weight: bold;
  color: #333;
  border: 1px solid #eee;
  vertical-align: middle;
  word-break: break-all;
}

.print-label-footer {
  display: flex;
  justify-content: flex-end;
  padding: 15px 20px;
  border-top: 1px solid #eee;
  gap: 10px;

  .footer-btn {
    width: 100px;
  }
}

/* 响应式设计 - 适配不同屏幕尺寸 */
/* 平板设备适配 (768px - 1024px) */
@media screen and (max-width: 1024px) {
  .qr-code-cell {
    .qr-code-img {
      
      width: 120px;
      height: 120px;
    }
  }

  .info-label {
    font-size: 13px;
    padding: 8px 12px;
  }

  .info-value {
    font-size: 15px;
    padding: 8px 12px;
  }
}

/* 移动设备适配 (480px - 768px) */
@media screen and (max-width: 768px) {
  .print-label-content {
    padding: 15px;
  }

  .qr-code-cell {
    width: 35%;
    padding: 8px;

    .qr-code-img {
      width: 100px;
      height: 100px;
    }
  }

  .info-label {
    width: 30%;
    font-size: 12px;
    padding: 6px 10px;
  }

  .info-value {
    width: 35%;
    font-size: 14px;
    padding: 6px 10px;
  }

  .print-label-header {
    padding: 12px 15px;

    .title {
      font-size: 16px;
    }
  }

  .print-label-footer {
    padding: 12px 15px;

    .footer-btn {
      width: 80px;
      font-size: 14px;
    }
  }
}

/* 小屏幕移动设备适配 (最大480px) */
@media screen and (max-width: 480px) {
  .print-label-content {
    padding: 10px;
  }

  .qr-code-cell {
    width: 40%;
    padding: 5px;

    .qr-code-img {
      width: 80px;
      height: 80px;
    }
  }

  .info-label {
    width: 25%;
    font-size: 11px;
    padding: 5px 8px;
  }

  .info-value {
    width: 35%;
    font-size: 13px;
    padding: 5px 8px;
  }

  .print-label-header {
    padding: 10px 12px;

    .title {
      font-size: 15px;
    }

    .close-icon {
      font-size: 18px;
    }
  }

  .print-label-footer {
    padding: 10px 12px;
    gap: 8px;

    .footer-btn {
      width: 70px;
      font-size: 13px;
    }
  }
}
</style>
