<template>
  <wd-popup v-model="show" position="center" custom-style="width: 80%; padding: 20px;">
    <view class="print-label-container">
      <view class="print-label-header">
        <text class="title">标签信息</text>
        <wd-icon name="close" @click="handleCancel" class="close-icon" />
      </view>
      <view class="print-label-content">
        <view class="left-qr-code">
          <image :src="qrCodeUrl" mode="scaleToFill" class="qr-code-img" />
        </view>
        <view class="right-info">
          <view class="info-item">
            <text class="label">物料名:</text>
            <text class="value">{{ materialName }}</text>
          </view>
          <view class="info-item">
            <text class="label">时间:</text>
            <text class="value">{{ time }}</text>
          </view>
          <view class="info-item">
            <text class="label">追溯码:</text>
            <text class="value">{{ traceCode }}</text>
          </view>
        </view>
      </view>
      <view class="print-label-footer">
        <wd-button @click="handleCancel" class="footer-btn">取消</wd-button>
        <wd-button type="primary" @click="handlePrint" class="footer-btn">打印</wd-button>
      </view>
    </view>
  </wd-popup>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import QRCode from 'qrcode'

const props = defineProps<{
  show: boolean
  materialName: string
  time: string
  traceCode: string
}>()

const emit = defineEmits(['update:show', 'print', 'cancel'])

const show = ref(props.show)

watch(
  () => props.show,
  (val) => {
    show.value = val
  }
)

watch(
  () => show.value,
  (val) => {
    emit('update:show', val)
  }
)

const qrCodeUrl = ref('')

const generateQRCode = async (text: string) => {
  try {
    qrCodeUrl.value = await QRCode.toDataURL(text, { errorCorrectionLevel: 'H', width: 200 })
  } catch (err) {
    console.error(err)
  }
}

watch(
  () => props.traceCode,
  (val) => {
    if (val) {
      generateQRCode(val)
    }
  },
  { immediate: true }
)

const handlePrint = () => {
  emit('print')
}

const handleCancel = () => {
  emit('cancel')
  show.value = false
}
</script>

<style lang="scss" scoped>
.print-label-container {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.print-label-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;

  .title {
    font-size: 18px;
    font-weight: bold;
  }

  .close-icon {
    font-size: 20px;
    color: #999;
  }
}

.print-label-content {
  display: flex;
  padding: 20px;
  gap: 20px;
}

.left-qr-code {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  .qr-code-img {
    width: 150px;
    height: 150px;
  }
}

.right-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

.info-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 10px;

  .label {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
  }

  .value {
    font-size: 16px;
    font-weight: bold;
    color: #333;
  }
}

.print-label-footer {
  display: flex;
  justify-content: flex-end;
  padding: 15px 20px;
  border-top: 1px solid #eee;
  gap: 10px;

  .footer-btn {
    width: 100px;
  }
}
</style>
