<route lang="json5">
{
  needLogin: true,
  style: {
    navigationBarTitleText: '报工',
  },
}
</route>
<template>
  <view class="report-page">
    <!-- 工单信息区域 -->
    <view class="info-section">
      <view class="section-title">
        <view class="title-line"></view>
        <text>工单信息</text>
      </view>

      <view class="info-list">
        <view class="info-item">
          <text class="info-label">生产机台：</text>
          <text class="info-value">{{ workOrder.deviceNumber }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">工单时间：</text>
          <text class="info-value">{{ workOrder.scheduleDate }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">工单班次：</text>
          <text class="info-value">{{ workOrder.shift }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">工单工件名：</text>
          <text class="info-value">{{ workOrder.materialName }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">加工单编号：</text>
          <text class="info-value">{{ workOrder.machineOrderNum }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">计划数量：</text>
          <text class="info-value">{{ workOrder.planQuantity }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">已报工数量：</text>
          <text class="info-value">{{ workOrder.workReportQuantity }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">备注：</text>
          <text class="info-value">{{ workOrder.remark }}</text>
        </view>
        <!-- 
        因为工单信息是通过扫描机台码带出来的信息，所以会判断是CNC还是压铸，如果是CNC这边就会显示夹具编号
        压铸就显示模具编号
        -->
        <view class="info-item">
          <text class="info-label">模具/夹具编号：</text>
          <text class="info-value">{{ workOrder.deviceNo }}</text>
        </view>
      </view>
    </view>

    <!-- 报工表单区域 -->
    <view class="report-section">
      <view class="section-title">
        <view class="title-line"></view>
        <text>报工</text>
      </view>

      <wd-form ref="form" :model="model" :rules="rules">
        <!-- 报工数量 -->
        <view class="form-item">
          <text class="form-label required">报工数量</text>
          <wd-input
            v-model="model.reportQuantity"
            :maxlength="8"
            prop="reportQuantity"
            type="digit"
            required
          />
        </view>

        <!-- 生产工时 -->
        <view class="form-item">
          <text class="form-label required">生产工时(h):</text>
          <wd-input
            v-model="model.productHours"
            :maxlength="8"
            prop="productHours"
            type="number"
            placeholder="请输入"
            required
          />
        </view>

        <!-- 不良品数量 -->
        <view class="form-item">
          <text class="form-label required">不良品数量:</text>
          <wd-input
            v-model="model.defQuantity"
            :maxlength="8"
            prop="defQuantity"
            type="digit"
            placeholder="请输入"
            required
          />
        </view>

        <!-- 机台显示数量 -->
        <view class="form-item">
          <text class="form-label required">机台显示数量:</text>
          <wd-input
            v-model="model.displayQuantity"
            :maxlength="8"
            prop="displayQuantity"
            type="number"
            placeholder="请输入"
            required
          />
        </view>

        <!-- 停机工时 -->
        <view class="form-item">
          <text class="form-label">停机工时(h):</text>
          <wd-input v-model="model.changeHours" :maxlength="8" type="number" placeholder="请输入" />
        </view>

        <!-- 停机备注 -->
        <view class="form-item">
          <text class="form-label">停机备注:</text>
          <wd-input v-model="model.shutdownRemark" type="text" placeholder="请输入" />
        </view>

        <!-- 追溯码 -->
        <view class="form-item">
          <text class="form-label required">追溯码:</text>
          <wd-input
            v-model="model.traceCode"
            prop="traceCode"
            placeholder="请扫码或输入追溯码"
            required
          >
            <template #suffix>
              <wd-icon
                name="scan"
                size="22"
                color="#4080ff"
                @click="handleScan"
                style="margin-left: 8rpx; cursor: pointer"
              />
            </template>
          </wd-input>
        </view>

        <!-- 新追溯码 -->
        <view class="form-item">
          <text class="form-label">新追溯码:</text>
          <view style="display: flex; align-items: center">
            <wd-input
              v-model="model.nextTraceCode"
              placeholder="点击右侧按钮获取追溯码"
              style="flex: 1"
            />
            <wd-button
              type="primary"
              size="small"
              style="margin-left: 8rpx; width: 180rpx"
              @click="getNextTraceCode"
            >
              获取追溯码
            </wd-button>
          </view>
        </view>

        <!-- 停机原因 -->
        <view class="form-item">
          <text class="form-label">停机原因:</text>
          <wd-picker v-model="model.shutdownReason" :columns="shutdownReasons" />
        </view>
      </wd-form>
      <!-- 提交按钮 -->
      <view class="form-item">
        <wd-button class="w-full" @click="submitReport">提交</wd-button>
      </view>
    </view>
    <!-- 工单选择弹窗 -->
    <wd-popup v-model="showPopup" position="center">
      <wd-card title="选择工单" class="popup-card">
        <view
          v-for="(item, i) in machineOrderList"
          :key="i"
          class="popup-item"
          @click="close(item.machineTaskId)"
        >
          <wd-cell title="工单时间" :value="item.scheduleDate" />
          <wd-cell title="加工单编号" :value="item.machineOrderNum" />
        </view>
      </wd-card>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import {
  getOrderInfoBefore,
  getOrderInfo,
  addWorkReport,
  shutdownReasonList,
  getNextTraceCode as fetchNextTraceCode,
} from '@/service/report/work'
import { IWorkReportBeforeRes, IWorkOrderRes, IWorkReportForm } from '@/service/report/types'
import { FormRules } from 'wot-design-uni/components/wd-form/types'
import { QrcodeDecoder } from 'qrcode-decoder'
import { getObjectURL } from '@/utils/config'

const showPopup = ref(false)
const machineOrderList = ref<IWorkReportBeforeRes[]>([])
// 工单信息数据
const workOrder = ref<IWorkOrderRes>({})
// 报工表单数据
const form = ref()
const initialModel: IWorkReportForm = {
  machineTaskId: null,
  reportQuantity: null,
  productHours: null,
  defQuantity: 0,
  displayQuantity: null,
  changeHours: 0,
  shutdownRemark: '',
  traceCode: '',
  nextTraceCode: '',
  shutdownReason: '',
}

const model = reactive<IWorkReportForm>({ ...initialModel })

// 停机原因选项
const shutdownReasons = ref<string[]>()
const rules: FormRules = {
  displayQuantity: [
    {
      required: true,
      message: '请输入机台显示数量',
    },
  ],
  reportQuantity: [
    {
      required: true,
      message: '请输入报工数量',
      validator: (value) => {
        if (Number(value) <= 0) {
          // eslint-disable-next-line prefer-promise-reject-errors
          return Promise.reject('请输入正确的报工数量')
        } else {
          return Promise.resolve()
        }
      },
    },
  ],
  productHours: [
    {
      required: true,
      message: '请输入生产工时',
    },
  ],
  defQuantity: [
    {
      required: true,
      message: '请输入不良品数量',
      validator: (value) => {
        if (Number(value) > Number(model.reportQuantity)) {
          // eslint-disable-next-line prefer-promise-reject-errors
          return Promise.reject('不良品数量不能大于报工数量')
        } else {
          return Promise.resolve()
        }
      },
    },
  ],
  traceCode: [
    {
      required: true,
      message: '请输入追溯码',
    },
  ],
}

onLoad((option) => {
  try {
    console.log('option', option)
    if (option.machineNumber) {
      reportGetOrderInfoBefore(option.machineNumber)
    } else if (option.taskId) {
      getWorkData(option.taskId)
    }
    getShutdownReasonList()
  } catch (e) {
    console.error(e)
  }
})
const open = () => {
  showPopup.value = true
}

const close = (machineTaskId: number) => {
  getWorkData(machineTaskId)
  showPopup.value = false
}

const getWorkData = (machineTaskId: number) => {
  uni.showLoading({ title: '加载中' })
  getOrderInfo(machineTaskId)
    .then((res) => {
      console.log('res', res)
      workOrder.value = res.data
    })
    .catch((e) => {
      console.error('获取工单详情异常', e)
    })
    .finally(() => {
      uni.hideLoading()
    })
}

const reportGetOrderInfoBefore = async (machineNumber: string) => {
  uni.showLoading({ title: '加载中' })
  try {
    console.log('machineNumber', machineNumber)
    const res = await getOrderInfoBefore(machineNumber)
    console.log('res', res)
    if (res.data.length > 1) {
      open()
      machineOrderList.value = res.data
    } else {
      getWorkData(res.data[0].machineTaskId)
    }
  } catch (e) {
    console.error('获取报工数据异常', e)
  } finally {
    uni.hideLoading()
  }
}

const handleScan = () => {
  console.log('scanCode')
  // #ifdef H5
  scanCodeH5()
  // #endif

  // #ifdef APP-PLUS
  scanCodeAPP()
  // #endif
}

const scanCodeAPP = () => {
  uni.scanCode({
    success: (res) => {
      model.traceCode = res.result
    },
    fail: (err) => {
      console.error('扫码失败:', err)
      uni.showToast({
        title: '扫码失败',
        icon: 'error',
      })
    },
  })
}

const scanCodeH5 = () => {
  const qrcode = new QrcodeDecoder()
  uni.chooseImage({
    count: 1,
    success: (imgRes) => {
      qrcode
        .decodeFromImage(getObjectURL(imgRes.tempFiles[0]))
        .then((res) => {
          model.traceCode = res.data
        })
        .catch((err) => {
          uni.showToast({
            title: '扫码失败',
            icon: 'error',
          })
          console.log(err)
        })
    },
  })
}

const getShutdownReasonList = async () => {
  const res = await shutdownReasonList()
  shutdownReasons.value = res.data
}

// 表单提交方法
// 获取下一个追溯码
const getNextTraceCode = async () => {
  try {
    uni.showLoading({ title: '获取中' })
    const res = await fetchNextTraceCode()
    if (res.data) {
      model.nextTraceCode = res.data
    }
  } catch (error) {
    uni.showToast({
      title: '获取追溯码失败',
      icon: 'error',
    })
  } finally {
    uni.hideLoading()
  }
}

const submitReport = async () => {
  form.value.validate().then(async ({ valid }) => {
    if (valid) {
      uni.showLoading({
        title: '提交中...',
        mask: true,
      })
      model.machineTaskId = workOrder.value.machineTaskId
      const res = await addWorkReport(model)
      uni.hideLoading()
      if (res.data) {
        Object.assign(model, initialModel)
        uni.showModal({
          title: '提交成功',
          content: '是否继续报工？',
          cancelText: '返回首页',
          confirmText: '继续报工',
          success: function (res) {
            if (res.cancel) {
              // 点击取消时，执行
              uni.switchTab({
                url: '/pages/index/index',
              })
            }
          },
        })
      }
    }
  })
}

const back = () => {
  console.log('back')
  uni.switchTab({
    url: '/pages/index/index',
  })
}

const rightDrawer = () => {
  uni.navigateTo({
    url: '/pages/report/workList?machineTaskId=' + workOrder.value.machineTaskId,
  })
}
</script>

<style>
/* 页面整体样式 */
.report-page {
  min-height: 100vh;
  font-size: 28rpx;
  color: #333;
  background-color: #f5f5f5;
}
/* 导航栏样式 */
.nav-bar {
  position: relative;
  display: flex;
  align-items: center;
  height: 90rpx;
  padding: 0 30rpx;
  color: #fff;
  background-color: #4080ff;
}

.nav-left {
  width: 60rpx;
  font-size: 40rpx;
}

.nav-title {
  flex: 1;
  font-size: 36rpx;
  font-weight: 500;
  text-align: center;
}

.nav-right {
  width: 140rpx;
  font-size: 28rpx;
  text-align: right;
}
/* 区域标题样式 */
.section-title {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  font-size: 30rpx;
  font-weight: 500;
}

.title-line {
  width: 8rpx;
  height: 32rpx;
  margin-right: 16rpx;
  background-color: #4080ff;
  border-radius: 4rpx;
}
/* 信息区域样式 */
.info-section {
  margin-bottom: 20rpx;
  background-color: #fff;
}

.info-list {
  padding: 0 30rpx 20rpx;
}

.info-item {
  display: flex;
  padding: 12rpx 0;
  line-height: 1.5;
}

.info-label {
  width: 220rpx;
  color: #666;
}

.info-value {
  flex: 1;
  color: #333;
}
/* 表单区域样式 */
.report-section {
  padding-bottom: 40rpx;
  background-color: #fff;
}

.form-item {
  padding: 20rpx 30rpx;
}

.form-label {
  display: block;
  margin-bottom: 16rpx;
  color: #666;
}

.required::before {
  margin-right: 4rpx;
  color: #f56c6c;
  content: '*';
}

.form-input {
  box-sizing: border-box;
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 8rpx;
}

.bg-gray {
  background-color: #f5f5f5;
}
/* 批次码输入框样式 */
.batch-code-input {
  position: relative;
}

.scan-icon {
  position: absolute;
  top: 50%;
  right: 20rpx;
  font-size: 40rpx;
  color: #4080ff;
  transform: translateY(-50%);
}
/* 下拉选择框样式 */
.select-input {
  position: relative;
}

.arrow-icon {
  position: absolute;
  top: 50%;
  right: 20rpx;
  font-size: 32rpx;
  color: #999;
  transform: translateY(-50%);
}
/* 提交按钮样式 */
.submit-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 90%;
  height: 90rpx;
  margin: 40rpx auto 0;
  font-size: 32rpx;
  color: #fff;
  background-color: #4080ff;
  border-radius: 45rpx;
}

.popup-card {
  max-height: 70vh;
  overflow-y: auto;
}

.popup-item {
  margin-bottom: 12px;
  &:last-child {
    margin-bottom: 0;
  }
}
</style>
