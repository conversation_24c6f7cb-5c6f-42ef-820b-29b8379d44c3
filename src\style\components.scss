/**
 * S-MES移动端通用组件样式
 * 基于设计系统的组件样式定义
 * 作者: AI Assistant
 * 创建时间: 2025-06-18
 */

@import './design-system.scss';

/* ==================== 页面容器组件 ==================== */

// 标准页面容器
.page-container {
  min-height: 100vh;
  padding-bottom: 120rpx; // 为底部按钮预留空间
  background-color: $bg-page;
}

// 内容区域容器
.content-section {
  padding: $page-padding;
  margin-bottom: $section-margin;
  background-color: $bg-card;
  border-radius: $border-radius-base;
}

// 表单区域容器
.form-section {
  padding: $content-padding;
  margin-bottom: $section-margin;
  background-color: $bg-card;
  border-radius: $border-radius-base;
}

/* ==================== 标题组件 ==================== */

// 区块标题样式
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: $spacing-lg;
  
  .title-bar {
    width: 8rpx;
    height: 36rpx;
    margin-right: $spacing-md;
    background-color: $primary-color;
    border-radius: $border-radius-sm;
  }
  
  .title-text {
    font-size: $font-size-lg;
    font-weight: $font-weight-medium;
    color: $text-primary;
  }
}

// 页面主标题
.page-title {
  font-size: $font-size-xl;
  font-weight: $font-weight-bold;
  color: $text-primary;
  text-align: center;
  margin-bottom: $spacing-xl;
}

/* ==================== 表单组件 ==================== */

// 表单项容器
.form-item {
  display: flex;
  flex-direction: column;
  margin-bottom: $spacing-lg;
  
  &:last-child {
    margin-bottom: 0;
  }
}

// 表单标签
.form-label {
  margin-bottom: $spacing-sm;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  color: $text-secondary;
}

// 表单值显示
.form-value {
  font-size: $font-size-base;
  color: $text-primary;
  word-break: break-all;
  line-height: $line-height-base;
}

// 扫描输入框组合样式
.input-with-scan {
  display: flex;
  align-items: center;
  padding-right: $spacing-sm;
  background-color: $bg-input;
  border-radius: $border-radius-base;
  border: 1rpx solid $border-light;
  
  .scan-input {
    flex: 1;
    background-color: transparent;
    border: none;
  }
  
  .scan-btn {
    padding: $spacing-xs;
    cursor: pointer;
    
    &:active {
      opacity: 0.7;
    }
  }
  
  .scan-icon {
    font-size: 40rpx;
    color: $primary-color;
  }
}

/* ==================== 按钮组件 ==================== */

// 底部固定按钮区域
.action-section {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: $z-index-fixed;
  padding: $spacing-md $page-padding;
  background-color: $bg-card;
  border-top: 1rpx solid $border-light;
  box-shadow: $shadow-light;
}

// 提交按钮（兼容旧命名）
.submit-btn,
.submit-button {
  @extend .action-section;
}

// 按钮组
.button-group {
  display: flex;
  gap: $spacing-sm;
  
  .button-item {
    flex: 1;
  }
}

/* ==================== 卡片组件 ==================== */

// 信息卡片
.info-card {
  padding: $content-padding;
  margin-bottom: $section-margin;
  background-color: $bg-card;
  border-radius: $border-radius-base;
  box-shadow: $shadow-light;
}

// 数据展示卡片
.data-card {
  @extend .info-card;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-md;
    padding-bottom: $spacing-sm;
    border-bottom: 1rpx solid $border-light;
    
    .card-title {
      font-size: $font-size-lg;
      font-weight: $font-weight-medium;
      color: $text-primary;
    }
    
    .card-status {
      padding: $spacing-xs $spacing-sm;
      border-radius: $border-radius-sm;
      font-size: $font-size-sm;
      font-weight: $font-weight-medium;
      
      &.status-success {
        background-color: rgba($success-color, 0.1);
        color: $success-color;
      }
      
      &.status-warning {
        background-color: rgba($warning-color, 0.1);
        color: $warning-color;
      }
      
      &.status-error {
        background-color: rgba($error-color, 0.1);
        color: $error-color;
      }
      
      &.status-info {
        background-color: rgba($info-color, 0.1);
        color: $info-color;
      }
    }
  }
  
  .card-content {
    .data-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: $spacing-sm;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .data-label {
        font-size: $font-size-base;
        color: $text-secondary;
      }
      
      .data-value {
        font-size: $font-size-base;
        color: $text-primary;
        font-weight: $font-weight-medium;
      }
    }
  }
}

/* ==================== 列表组件 ==================== */

// 列表容器
.list-container {
  padding: 0 $page-padding;
}

// 列表项
.list-item {
  @extend .data-card;
  margin-bottom: $spacing-md;
  
  &:last-child {
    margin-bottom: 0;
  }
}

/* ==================== 上传组件 ==================== */

// 图片上传容器
.upload-container {
  margin-top: $spacing-sm;
  padding: 0 $spacing-lg;
}

/* ==================== 状态组件 ==================== */

// 状态标签
.status-tag {
  display: inline-block;
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-sm;
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  
  &.tag-primary {
    background-color: rgba($primary-color, 0.1);
    color: $primary-color;
  }
  
  &.tag-success {
    background-color: rgba($success-color, 0.1);
    color: $success-color;
  }
  
  &.tag-warning {
    background-color: rgba($warning-color, 0.1);
    color: $warning-color;
  }
  
  &.tag-error {
    background-color: rgba($error-color, 0.1);
    color: $error-color;
  }
}

/* ==================== 质量检验模块专用样式 ==================== */

// 质量检验状态样式
.quality-status {
  &.quality-pass {
    color: $quality-pass;
  }
  
  &.quality-fail {
    color: $quality-fail;
  }
  
  &.quality-pending {
    color: $quality-pending;
  }
}

// 质量检验卡片
.quality-card {
  @extend .data-card;
  border-left: 4rpx solid $quality-primary;
}
