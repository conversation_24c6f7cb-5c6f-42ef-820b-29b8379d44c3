<template>
  <view class="lime-echart" :style="customStyle" v-if="canvasId">
    <!-- #ifndef APP-NVUE -->
    <canvas
      class="lime-echart__canvas"
      v-if="use2dCanvas"
      type="2d"
      :id="canvasId"
      :style="'width:' + width + 'px;height:' + height + 'px'"
      :disable-scroll="isDisableScroll"
      @touchstart="touchStart"
      @touchmove="touchMove"
      @touchend="touchEnd"
    />
    <canvas
      class="lime-echart__canvas"
      v-else
      :width="nodeWidth"
      :height="nodeHeight"
      :style="'width:' + width + 'px;height:' + height + 'px'"
      :canvas-id="canvasId"
      :id="canvasId"
      :disable-scroll="isDisableScroll"
      @touchstart="touchStart"
      @touchmove="touchMove"
      @touchend="touchEnd"
    />
    <!-- #endif -->
    <!-- #ifdef APP-NVUE -->
    <web-view
      class="lime-echart__canvas"
      :id="canvasId"
      :webview-styles="webviewStyles"
      ref="webview"
      src="http://liangei.gitee.io/limeui/hybrid/html/lime-ui/lime-echart/index.html?v=0.2.3"
      @pagefinish="finished = true"
      @onPostMessage="onMessage"
    ></web-view>
    <!-- #endif -->
  </view>
</template>

<script setup>
import { ref, computed, watch, onMounted, onBeforeUnmount, getCurrentInstance } from 'vue'

// #ifndef APP-NVUE
import Canvas from './canvas'
import * as echarts from 'echarts/core'
import { compareVersion, wrapTouch, devicePixelRatio } from './utils'
// #endif
// #ifdef APP-NVUE
import { base64ToPath } from './utils'
// #endif

// Props定义
const props = defineProps({
  // #ifdef MP-WEIXIN || MP-TOUTIAO
  type: {
    type: String,
    default: '2d',
  },
  // #endif
  ec: {
    type: Object,
    default: () => ({}),
  },
  onInit: {
    type: Function,
    default: null,
  },
  // #ifdef APP-NVUE
  webviewStyles: {
    type: Object,
    default: null,
  },
  params: {
    type: Object,
    default: () => ({}),
  },
  // #endif
  customStyle: String,
  isAutoPlay: Boolean,
  isDisableScroll: Boolean,
  isEnable: Boolean,
  isClickable: {
    type: Boolean,
    default: true,
  },
})

// Emits定义
const emit = defineEmits(['inited', 'onCanvasToTempFilePath'])

// 生成唯一ID
const instance = getCurrentInstance()
const uniqueId = ref(`lime-echart-${instance?.uid || Math.floor(Math.random() * 10000)}`)

// 响应式状态
const use2dCanvas = ref(false)
// #ifdef MP-WEIXIN || MP-TOUTIAO
use2dCanvas.value = true
// #endif

const width = ref(null)
const height = ref(null)
const nodeWidth = ref(null)
const nodeHeight = ref(null)
const canvasNode = ref(null)
const config = ref({})
const inited = ref(false)
const finished = ref(false)
const file = ref('')
const platform = ref('')
const chart = ref(null)
const isPC = ref(false)
const webview = ref(null)

// 计算属性
const canvasId = computed(() => uniqueId.value)

// 方法
const setCanvasNode = (node) => {
  // #ifndef APP-NVUE
  canvasNode.value = node
  config.value.node = node
  canvasNode.value.width = width.value
  canvasNode.value.height = height.value
  const ctx = canvasNode.value.getContext('2d')
  const canvas = new Canvas(ctx, canvasId.value, true, canvasNode.value)
  echarts.setCanvasCreator(() => {
    return canvas
  })
  config.value.canvas = canvas
  // #endif
}

const touchStart = (e) => {
  // #ifndef APP-NVUE
  // 非 canvas 组件事件
  if (e.touches[0].identifier == 'canvas') return
  // 阻止屏幕滚动
  if (props.isDisableScroll) document.addEventListener('touchmove', stopEvent, { passive: false })
  chart.value &&
    chart.value.dispatchAction({
      type: 'hideTip',
    })
  chart.value &&
    props.isClickable &&
    chart.value.getZr().handler.dispatch('mousedown', {
      zrX: e.touches[0].x,
      zrY: e.touches[0].y,
      preventDefault: () => {
        return false
      },
    })
  chart.value &&
    props.isClickable &&
    chart.value.getZr().handler.dispatch('mousemove', {
      zrX: e.touches[0].x,
      zrY: e.touches[0].y,
      preventDefault: () => {
        return false
      },
    })
  // #endif
}

const touchMove = (e) => {
  // #ifndef APP-NVUE
  chart.value &&
    props.isClickable &&
    chart.value.getZr().handler.dispatch('mousemove', {
      zrX: e.touches[0].x,
      zrY: e.touches[0].y,
      preventDefault: () => {
        return false
      },
    })
  // #endif
}

const touchEnd = (e) => {
  // #ifndef APP-NVUE
  // 非 canvas 组件事件
  if (e.changedTouches[0].identifier == 'canvas') return
  // 重新允许屏幕滚动
  if (props.isDisableScroll) document.removeEventListener('touchmove', stopEvent)
  chart.value &&
    props.isClickable &&
    chart.value.getZr().handler.dispatch('mouseup', {
      zrX: e.changedTouches[0].x,
      zrY: e.changedTouches[0].y,
      preventDefault: () => {
        return false
      },
    })
  chart.value &&
    props.isClickable &&
    chart.value.getZr().handler.dispatch('click', {
      zrX: e.changedTouches[0].x,
      zrY: e.changedTouches[0].y,
      preventDefault: () => {
        return false
      },
    })
  // #endif
}

const stopEvent = (e) => {
  // 阻止屏幕滚动
  e.preventDefault()
}

// #ifdef APP-NVUE
const getParent = () => {
  let parent = instance.parent
  let parentRefs = Object.values(parent.refs || {})
  while (!parentRefs.length || parentRefs.find((v) => v && v.uid === instance.uid)) {
    parent = parent.parent
    parentRefs = Object.values(parent.refs || {})
    if (!parent) return false
  }
  return parent
}

const getWebview = () => {
  if (finished.value) {
    return Promise.resolve(finished.value)
  }
  return new Promise((resolve) => {
    watch(finished, (newVal) => {
      if (newVal) {
        resolve(newVal)
      }
    })
  })
}

const onMessage = (e) => {
  const res = e?.detail?.data[0] || null
  if (res?.event) {
    if (res.event === 'inited') {
      inited.value = true
    }
    emit(res.event, JSON.parse(res.data))
  } else if (res?.file) {
    file.value = res.data
  } else if (!res[0] && JSON.stringify(res[0]) != '{}') {
    console.error(res)
  }
}
// #endif

const enabled = async () => {
  // #ifndef APP-NVUE
  if (!chart.value || !inited.value) {
    return await init()
  } else {
    return chart.value
  }
  // #endif

  // #ifdef APP-NVUE
  await getWebview()
  if (!inited.value) {
    return new Promise((resolve) => {
      watch(inited, (newVal) => {
        if (newVal) {
          resolve(newVal)
        }
      })
    })
  }
  return inited.value
  // #endif
}

const setChart = async (callback, params) => {
  await enabled()
  // #ifndef APP-NVUE
  if (typeof callback === 'function' && chart.value) {
    await callback(chart.value)
  }
  // #endif
  // #ifdef APP-NVUE
  if (typeof callback === 'function') {
    webview.value.evalJs(
      `setChart(${JSON.stringify(callback.toString())}, ${JSON.stringify(params || props.params)})`,
    )
  }
  // #endif
}

const setOption = async (...args) => {
  await enabled()
  // #ifndef APP-NVUE
  if (!chart.value || !chart.value.setOption) {
    console.warn(`组件还未初始化`)
  } else {
    chart.value.setOption(...args)
  }
  // #endif
  // #ifdef APP-NVUE
  webview.value.evalJs(`setOption(${JSON.stringify(args)})`)
  // #endif
}

const showLoading = async () => {
  // #ifndef APP-NVUE
  await enabled()
  if (chart.value) {
    chart.value.showLoading()
  }
  // #endif
  // #ifdef APP-NVUE
  webview.value.evalJs(`showLoading()`)
  // #endif
}

const hideLoading = () => {
  // #ifndef APP-NVUE
  if (chart.value) {
    chart.value.hideLoading()
  }
  // #endif
  // #ifdef APP-NVUE
  webview.value.evalJs(`hideLoading()`)
  // #endif
}

const clear = () => {
  // #ifndef APP-NVUE
  if (chart.value) {
    chart.value.clear()
  }
  // #endif
  // #ifdef APP-NVUE
  webview.value?.evalJs(`clear()`)
  // #endif
}

const dispose = () => {
  // #ifndef APP-NVUE
  if (chart.value) {
    chart.value.dispose()
  }
  // #endif
  // #ifdef APP-NVUE
  webview.value?.evalJs(`dispose()`)
  // #endif
}

const canvasToTempFilePath = (opt) => {
  return new Promise((resolve, reject) => {
    // #ifndef APP-NVUE
    if (!chart.value) return
    const success = opt?.success || null
    const error = opt?.error || null
    if (opt) {
      opt.success = (res) => {
        success && success(res)
        resolve(res)
      }
      opt.error = (err) => {
        error && error(err)
        reject(err)
      }
    }
    opt.canvasId = canvasId.value
    if (use2dCanvas.value) {
      const ctx = canvasNode.value.getContext('2d')
      opt.canvas = canvasNode.value
    }
    uni.canvasToTempFilePath(opt, getCurrentInstance())
    // #endif
    // #ifdef APP-NVUE
    if (file.value) {
      resolve({ tempFilePath: file.value })
    } else {
      webview.value.evalJs(`canvasToTempFilePath()`)
      const unwatch = watch(() =>
        emit('onCanvasToTempFilePath', async (res) => {
          file.value = await base64ToPath(res.base64)
          resolve({ tempFilePath: file.value })
          unwatch()
        }),
      )
    }
    // #endif
  })
}

const init = (callback) => {
  return new Promise((resolve, reject) => {
    uni
      .createSelectorQuery()
      .in(getCurrentInstance())
      .select('.lime-echart')
      .fields({
        node: true,
        size: true,
      })
      .exec((res) => {
        if (res[0]) {
          width.value = res[0].width
          height.value = res[0].height
          config.value.width = res[0].width * devicePixelRatio
          config.value.height = res[0].height * devicePixelRatio
          nodeWidth.value = res[0].width * devicePixelRatio
          nodeHeight.value = res[0].height * devicePixelRatio
          // #ifndef APP-NVUE
          if (use2dCanvas.value) {
            setCanvasNode(res[0].node)
          }
          // #endif
          try {
            // #ifndef APP-NVUE
            const { width: configWidth, height: configHeight, canvas } = config.value
            const chartInstance = echarts.init(canvas, null, {
              width: configWidth,
              height: configHeight,
              devicePixelRatio,
              renderer: 'canvas',
            })
            chart.value = chartInstance
            canvas.setChart(chartInstance)
            inited.value = true
            if (props.ec && props.ec.option) {
              setOption(props.ec.option, props.ec.notMerge, props.ec.lazyUpdate)
            }
            if (typeof callback === 'function') {
              callback({
                chart: chartInstance,
                canvas,
                width: width.value,
                height: height.value,
                devicePixelRatio,
                id: canvasId.value,
                setChart: (newChart) => {
                  if (!newChart) return
                  chart.value = newChart
                  canvas.setChart(chart.value)
                },
              })
            }
            resolve({
              chart: chartInstance,
              canvas,
              width: width.value,
              height: height.value,
              devicePixelRatio,
              id: canvasId.value,
              setChart: (newChart) => {
                if (!newChart) return
                chart.value = newChart
                canvas.setChart(chart.value)
              },
            })
            // #endif
            // #ifdef APP-NVUE
            webview.value.evalJs(
              `init(${JSON.stringify({
                width: width.value,
                height: height.value,
                devicePixelRatio,
                id: canvasId.value,
                cb: callback ? callback.toString() : null,
                option: props.ec && props.ec.option ? JSON.stringify(props.ec.option) : null,
                notMerge: props.ec && props.ec.notMerge,
                lazyUpdate: props.ec && props.ec.lazyUpdate,
                params: props.params ? JSON.stringify(props.params) : null,
              })})`,
            )
            // #endif
          } catch (e) {
            reject(e)
          }
        } else {
          reject(`画布获取失败`)
        }
      })
  })
}

// 生命周期
onMounted(() => {
  // #ifndef APP-NVUE
  echarts.registerPreprocessor((option) => {
    if (option && option.series) {
      if (option.series.length > 0) {
        option.series.forEach((series) => {
          series.progressive = 0
        })
      } else if (typeof option.series === 'object') {
        option.series.progressive = 0
      }
    }
  })

  // #ifdef MP-WEIXIN || MP-TOUTIAO
  const { SDKVersion, version, platform: platformVal, environment } = uni.getSystemInfoSync()
  platform.value = platformVal
  // #endif

  // #ifdef MP-WEIXIN
  isPC.value = /windows/i.test(platform.value)
  use2dCanvas.value =
    props.type === '2d' &&
    compareVersion(SDKVersion, '2.9.2') >= 0 &&
    !((/ios/i.test(platform.value) && /7.0.20/.test(version)) || /wxwork/i.test(environment)) &&
    !isPC.value
  // #endif

  // #ifdef MP-TOUTIAO
  isPC.value = /devtools/i.test(platform.value)
  use2dCanvas.value = props.type === '2d' && compareVersion(SDKVersion, '1.78.0') >= 0
  // #endif
  // #endif

  if ((props.ec && !props.ec?.lazyLoad) || props.isEnable) {
    if (!props.onInit) {
      init()
    } else {
      init(props.onInit)
    }
  }
})

onBeforeUnmount(() => {
  clear()
  dispose()
})

// 监听器
watch(
  () => props.ec?.option,
  (val) => {
    if (props.isAutoPlay) {
      if (props.ec.clear) {
        clear()
      }
      setOption(val)
    }
  },
  { deep: true },
)

// 暴露方法供父组件调用
defineExpose({
  init,
  setChart,
  setOption,
  showLoading,
  hideLoading,
  clear,
  dispose,
  canvasToTempFilePath,
  chart,
})
</script>

<style scoped>
.lime-echart {
  position: relative;
  width: 100%;
  height: 100%;
}
.lime-echart__canvas {
  width: 100%;
  height: 100%;
}
</style>
