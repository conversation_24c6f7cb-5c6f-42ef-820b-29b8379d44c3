---
description: 
globs: 
alwaysApply: true
---
# 应用架构指南

## 总体架构

项目基于uni-app框架，使用Vue 3 + TypeScript + Pinia + wot ui开发，采用了现代化的前端架构设计。

## 状态管理

项目使用Pinia进行状态管理，配合`pinia-plugin-persistedstate`插件实现状态持久化。状态管理文件存放在`src/store`目录下。

## 拦截器系统

项目实现了完整的拦截器系统，集中在`src/interceptors`目录下：

- [route.ts](mdc:src/interceptors/route.ts) - 路由拦截器，实现登录保护
- [request.ts](mdc:src/interceptors/request.ts) - 请求拦截器，处理请求头、基础URL等
- [prototype.ts](mdc:src/interceptors/prototype.ts) - 原型扩展拦截器

## API服务

API服务按模块划分，位于`src/service`目录，主要包括：

- `app` - 应用通用服务
- `login` - 登录相关服务
- `index` - 首页相关服务

## 路由系统

项目使用uni-app页面路由系统，主要通过以下方式实现：

1. `pages.json` - 基础路由配置
2. `@uni-helper/vite-plugin-uni-pages` - 自动生成路由配置
3. `@uni-helper/vite-plugin-uni-layouts` - 页面布局系统

## 组件设计

组件按照功能划分，存放在`src/components`目录下，遵循单一职责原则。

