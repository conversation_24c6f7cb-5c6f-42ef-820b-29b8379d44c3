<route lang="json5">
{
  needLogin: true,
  style: {
    navigationBarTitleText: '检验详情',
  },
}
</route>

<template>
  <view class="container">
    <view v-if="loading" class="loading-container">
      <text class="loading-text">加载中...</text>
    </view>
    <view v-else class="detail-card">
      <!-- 标题和状态标签 -->
      <view class="card-header">
        <text class="card-title">{{ detailData.productName }}</text>
        <wd-tag :type="getStatusType(detailData.status)">{{ detailData.statusText }}</wd-tag>
      </view>

      <!-- 工单信息 -->
      <inspection-info-card title="工单信息" :info-items="orderInfoItems" />

      <!-- 检验结果 -->
      <view class="card-section">
        <view class="section-title">检验结果</view>
        <view class="info-grid">
          <view class="info-item">
            <text class="info-label">批次码</text>
            <text class="info-value">{{ detailData.batchNo }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">质检类型</text>
            <text class="info-value">{{ detailData.inspectionType }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">检验人</text>
            <text class="info-value">{{ detailData.inspector }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">检验时间</text>
            <text class="info-value">{{ detailData.inspectionTime }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">不良品数量</text>
            <text class="info-value">{{ detailData.defectiveQuantity }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">不良率</text>
            <text class="info-value">{{ defectiveRate }}%</text>
          </view>
        </view>
      </view>

      <!-- 检验条件 -->
      <view class="card-section" v-if="detailData.inspectionCondition">
        <view class="section-title">检验条件</view>
        <view class="remark-text">{{ detailData.inspectionCondition }}</view>
      </view>

      <!-- 条件备注 -->
      <view class="card-section" v-if="detailData.conditionRemark">
        <view class="section-title">条件备注</view>
        <view class="remark-text">{{ detailData.conditionRemark }}</view>
      </view>

      <!-- 备注信息 -->
      <view class="card-section" v-if="detailData.remark">
        <view class="section-title">备注信息</view>
        <view class="remark-text">{{ detailData.remark || '无' }}</view>
      </view>

      <!-- 检验图片 -->
      <view class="card-section" v-if="detailData.images && detailData.images.length > 0">
        <view class="section-title">检验图片</view>
        <view class="image-grid">
          <view
            class="image-item"
            v-for="(image, index) in detailData.images"
            :key="index"
            @click="previewImage(index)"
          >
            <image class="thumbnail" :src="image" mode="aspectFill" />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import InspectionInfoCard from '@/components/quality/InspectionInfoCard.vue'

// 获取页面参数
const inspectionId = ref(0)

// 定义加载状态
const loading = ref(true)

// 定义详情数据
const detailData = ref({
  id: 0,
  productName: '',
  batchNo: '',
  productionLine: '',
  scheduleTime: '',
  workOrder: '',
  planQuantity: 0,
  quantity: 0,
  shift: '',
  unit: '件',
  inspector: '',
  inspectionTime: '',
  inspectionType: '',
  inspectionCondition: '',
  conditionRemark: '',
  defectiveQuantity: 0,
  status: 0,
  statusText: '',
  remark: '',
  images: [],
})

// 计算工单信息数据
const orderInfoItems = computed(() => [
  { label: '生产机台', value: detailData.value.productionLine },
  { label: '排程时间', value: detailData.value.scheduleTime },
  { label: '加工单', value: detailData.value.workOrder },
  { label: '计划数量', value: detailData.value.planQuantity, unit: detailData.value.unit },
  { label: '预入库数量', value: detailData.value.quantity, unit: detailData.value.unit },
  { label: '班次', value: detailData.value.shift },
])

// 计算不良率
const defectiveRate = computed(() => {
  if (detailData.value.quantity === 0) return '0.00'
  const rate = (detailData.value.defectiveQuantity / detailData.value.quantity) * 100
  return rate.toFixed(2)
})

// 获取详情数据
const fetchDetail = (id: number) => {
  loading.value = true

  // 模拟API请求
  setTimeout(() => {
    // 这里应该是实际的API调用
    const mockData = {
      id,
      productName: '电动马达总成',
      batchNo: 'B2024052901',
      productionLine: '智能数控',
      scheduleTime: '2024-05-29 08:00',
      workOrder: 'WO-202405-001',
      planQuantity: 200,
      quantity: 200,
      unit: '件',
      shift: '早班',
      inspectionType: '全检',
      inspectionCondition: '按照SOP流程进行检验，重点检查转轴部分',
      conditionRemark: '测试备注信息',
      inspector: '张三',
      inspectionTime: '2024-05-29 16:30',
      defectiveQuantity: 0,
      status: 1,
      statusText: '合格',
      remark: '本批次产品质量良好，所有参数符合要求。',
      images: [
        'https://ai-public.mastergo.com/ai/img_res/a32878975ba049af4fa11a05b05609a1.jpg',
        'https://ai-public.mastergo.com/ai/img_res/6ef2fca8993cc395769edd81f6342999.jpg',
      ],
    }

    detailData.value = mockData
    loading.value = false
  }, 500)
}

// 根据状态获取标签类型
const getStatusType = (status: number) => {
  switch (status) {
    case 1:
      return 'success'
    case 2:
      return 'danger'
    default:
      return 'primary'
  }
}

// 预览图片
const previewImage = (index: number) => {
  uni.previewImage({
    urls: detailData.value.images,
    current: index,
  })
}

onMounted(() => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  // @ts-expect-error $page对象在uni-app类型定义中可能缺失
  const id = Number(currentPage.$page?.options?.id || 0)
  inspectionId.value = id

  // 获取详情数据
  if (id) {
    fetchDetail(id)
  } else {
    uni.showToast({
      title: '参数错误',
      icon: 'none',
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
})
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding-bottom: 120rpx;
  background-color: #f5f5f5;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999999;
}

.detail-card {
  padding: 30rpx;
  margin: 20rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 20rpx;
  margin-bottom: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.card-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333333;
}

.card-section {
  padding-bottom: 20rpx;
  margin-bottom: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.card-section:last-child {
  padding-bottom: 0;
  margin-bottom: 0;
  border-bottom: none;
}

.section-title {
  margin-bottom: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.info-grid {
  display: flex;
  flex-wrap: wrap;
}

.info-item {
  width: 50%;
  margin-bottom: 20rpx;
}

.info-label {
  display: block;
  margin-bottom: 8rpx;
  font-size: 28rpx;
  color: #999999;
}

.info-value {
  font-size: 30rpx;
  color: #333333;
}

.remark-text {
  padding: 20rpx;
  font-size: 28rpx;
  line-height: 1.6;
  color: #666666;
  background-color: #f9f9f9;
  border-radius: 8rpx;
}

.image-grid {
  display: flex;
  flex-wrap: wrap;
}

.image-item {
  width: 220rpx;
  height: 220rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}

.thumbnail {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}
</style>
