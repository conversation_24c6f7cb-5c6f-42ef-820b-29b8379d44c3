// 首页待办任务
import { http } from '@/utils/http'

export const todayTaskList = () => {
  return http.get<ITodayTaskRes>('/api/v1/terminal/schedule/todayTask', {})
}

// 我的任务列表
export const myTaskList = (params?: IMyTaskParams) => {
  return http.get<ITodayTaskRes>('/api/v1/terminal/schedule/myTask', params || {})
}

// 我的任务查询参数接口
export interface IMyTaskParams {
  page?: number
  size?: number
  startTime?: string
  endTime?: string
}

export interface ITodayTaskRes {
  completed: number
  content: []
  total: number
}
