<route lang="json5">
{
  type: 'page',
  needLogin: true,
  style: {
    navigationBarTitleText: '生产进度',
    enablePullDownRefresh: true,
  },
}
</route>

<template>
  <view class="production-progress">
    <z-paging ref="paging" v-model="planList" @query="queryPlanList">
      <!-- 顶部统计区域 -->
      <view class="stats-header">
        <view class="stats-box">
          <view class="stats-item">
            <view class="stats-total" @click="loadPlans()">{{ planTotal }}</view>
            <view class="stats-label">总数</view>
          </view>
          <view class="stats-item">
            <circle-progress
              v-if="pieData.isReady"
              v-model:current="pieData.completedPercent"
              :percent="pieData.completedPercent"
              :size="100"
              :stroke-color="pieData.titleColor"
              :trail-color="'#F5F5F5'"
            >
              <view class="circle-content">
                <text class="circle-title">{{ pieData.titlePie }}</text>
                <text class="circle-percent">{{ pieData.titleNum }}</text>
              </view>
            </circle-progress>
          </view>
          <view class="stats-item stats-status">
            <view class="status-row" @click="loadPlans(2)">
              <view class="status-label">已完成</view>
              <view class="status-value completed">{{ pieData.dataPie[0].value }}</view>
            </view>
            <view class="status-row" @click="loadPlans(1)">
              <view class="status-label">进行中</view>
              <view class="status-value in-progress">{{ pieData.dataPie[1].value }}</view>
            </view>
            <view class="status-row" @click="loadPlans(0)">
              <view class="status-label">未开始</view>
              <view class="status-value not-started">{{ pieData.dataPie[2].value }}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 计划列表 -->
      <view class="plan-list">
        <view
          class="plan-item"
          v-for="(item, index) in planList"
          :key="index"
          @click="goToDetail(item)"
        >
          <view :class="['plan-status-bar', `status-${item.status}`]"></view>
          <view class="plan-content">
            <view
              :class="[
                'plan-status-text',
                {
                  completed: item.status === 2,
                  'in-progress': item.status === 1,
                  'not-started': item.status === 0,
                },
              ]"
            >
              {{ item.status === 2 ? '已完成' : item.status === 1 ? '进行中' : '未开始' }}
            </view>
            <view class="plan-info-row">
              <view class="info-label">生产计划编号</view>
              <view class="info-value">{{ item.proPlanNum }}</view>
            </view>
            <view class="plan-info-row">
              <view class="info-label">订单号</view>
              <view class="info-value">{{ item.orderNum }}</view>
            </view>
            <view class="plan-footer">
              <view class="plan-progress">
                <view class="progress-label">实际/计划</view>
                <view class="progress-value">
                  {{ item.completeQuantity }}/{{ item.planQuantity }}
                </view>
              </view>
              <view class="plan-defects">
                <view class="defects-label">不良</view>
                <view class="defects-value">{{ item.defQuantity }}</view>
              </view>
            </view>
          </view>
          <view class="plan-chart">
            <circle-progress
              v-model:current="item.progressPercent"
              :percent="item.progressPercent"
              :size="85"
              :stroke-color="item.titleColor"
              :trail-color="'#F5F5F5'"
              :stroke-width="8"
            >
              <view class="circle-content">
                <text
                  class="circle-title"
                  :style="{ color: item.titleColor, fontSize: item.titlePieSize + 'px' }"
                >
                  {{ item.titlePie }}
                </text>
                <text
                  class="circle-percent"
                  :style="{ color: item.titleColor, fontSize: item.titleNumSize + 'px' }"
                >
                  {{ item.titleNum }}
                </text>
              </view>
            </circle-progress>
          </view>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import CircleProgress from '@/components/charts/CircleProgress.vue'

// 分页参数
const paging = ref()
const pageSize = 10
const currentStatus = ref(null)

// 计划总数
const planTotal = ref(0)

// 饼图数据
const pieData = reactive({
  colorData: ['#36B876', '#F99E38', '#B5B5B5'],
  dataPie: [
    { value: 0, name: '已完成' },
    { value: 0, name: '进行中' },
    { value: 0, name: '未开始' },
  ],
  titleColor: '#36B876',
  titlePie: '已完成',
  titleNum: '0%',
  titlePieSize: 12,
  titleNumSize: 12,
  isReady: false,
  completedPercent: 0,
})

// 计划列表
const planList = ref<any[]>([])

// 加载统计数据
const loadStatistics = () => {
  // 模拟API请求
  setTimeout(() => {
    // 模拟数据
    const total = 100
    const completed = 30
    const inProgress = 50
    const notStarted = 20

    planTotal.value = total
    pieData.dataPie[0].value = completed
    pieData.dataPie[1].value = inProgress
    pieData.dataPie[2].value = notStarted
    pieData.titleNum = ((completed / total) * 100).toFixed(2) + '%'
    pieData.isReady = true
    pieData.completedPercent = (completed / total) * 100
  }, 500)
}

// 查询计划列表
const queryPlanList = (pageNo: number, pageSize: number) => {
  // 模拟API请求
  setTimeout(() => {
    const list = []
    const status = currentStatus.value

    // 生成模拟数据
    for (let i = 0; i < pageSize; i++) {
      // 如果指定了状态，则只生成该状态的数据
      const itemStatus = status !== null ? status : Math.floor(Math.random() * 3)

      // 根据状态设置不同的数据
      const item: any = {
        id: `PLAN${pageNo}${i}`,
        proPlanNum: `PP${new Date().getFullYear()}${String(pageNo).padStart(2, '0')}${String(i).padStart(3, '0')}`,
        orderNum: `ORD${new Date().getFullYear()}${Math.floor(Math.random() * 10000)}`,
        status: itemStatus,
        planQuantity: Math.floor(Math.random() * 100) + 50,
        completeQuantity: 0,
        defQuantity: Math.floor(Math.random() * 10),
        titlePieSize: 8,
        titleNumSize: 8,
        progressPercent: 0,
      }

      // 根据状态设置完成数量和图表数据
      if (itemStatus === 0) {
        // 未开始
        item.colorData = ['#F5F5F5', '#B5B5B5']
        item.titleColor = '#B5B5B5'
        item.titlePie = '未开始'
        item.titleNum = '0%'
        item.dataPie = [
          { value: 100, name: '未开始' },
          { value: 0, name: '进行中' },
        ]
        item.progressPercent = 0
      } else if (itemStatus === 1) {
        // 进行中
        item.completeQuantity = Math.floor(item.planQuantity * (Math.random() * 0.8 + 0.1))
        item.colorData = ['#F5F5F5', '#F99E38']
        item.titleColor = '#F99E38'
        item.titlePie = '进行中'
        item.titleNum = ((item.completeQuantity / item.planQuantity) * 100).toFixed(2) + '%'
        item.dataPie = [
          { value: item.planQuantity - item.completeQuantity, name: '未完成' },
          { value: item.completeQuantity, name: '已完成' },
        ]
        item.progressPercent = (item.completeQuantity / item.planQuantity) * 100
      } else {
        // 已完成
        item.completeQuantity = item.planQuantity
        item.colorData = ['#F5F5F5', '#36B876']
        item.titleColor = '#36B876'
        item.titlePie = '已完成'
        item.titleNum = '100%'
        item.dataPie = [
          { value: 0, name: '未完成' },
          { value: item.planQuantity, name: '已完成' },
        ]
        item.progressPercent = 100
      }

      list.push(item)
    }

    // 更新列表数据
    paging.value.complete(list)
  }, 1000)
}

// 根据状态加载计划
const loadPlans = (status = null) => {
  currentStatus.value = status
  paging.value.reload()
}

// 跳转到详情页
const goToDetail = (item: any) => {
  uni.navigateTo({
    url: `/pages/production/detail?id=${item.id}`,
  })
}

// 初始化
loadStatistics()
</script>

<style scoped>
.production-progress {
  width: 100%;
  background-color: #f3f3f3;
}
/* 顶部统计区域 */
.stats-header {
  padding: 16px 0;
  margin-bottom: 10px;
  background-color: #fff;
}

.stats-box {
  display: flex;
  justify-content: space-between;
  width: 90%;
  margin: 0 auto;
}

.stats-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 33.3%;
  height: 100px;
}

.stats-total {
  font-size: 30px;
  font-weight: 700;
  line-height: 75px;
  color: #666666;
  text-align: center;
}

.stats-label {
  font-size: 10px;
  color: #333333;
  text-align: left;
}

.stats-status {
  margin-top: 12px;
}

.status-row {
  display: flex;
  margin-bottom: 15px;
}

.status-label {
  width: 50px;
  margin-right: 10px;
  font-size: 10px;
  text-align: justify;
  text-align-last: justify;
}

.status-value {
  width: calc(100% - 60px);
  font-size: 10px;
  text-align: left;
}

.completed {
  color: #36b876;
}
.in-progress {
  color: #f99e38;
}
.not-started {
  color: #b5b5b5;
}
/* 计划列表 */
.plan-list {
  width: 100%;
}

.plan-item {
  display: flex;
  width: 94%;
  height: 115px;
  margin: 0 auto 10px;
  background-color: #fff;
  border-radius: 2px;
}

.plan-status-bar {
  width: 7px;
  height: 100%;
  border-radius: 2px 0 0 2px;
}

.status-0 {
  background-color: #b5b5b5;
}
.status-1 {
  background-color: #f99e38;
}
.status-2 {
  background-color: #36b876;
}

.plan-content {
  width: calc(100% - 7px - 100px);
  padding: 16px;
}

.plan-status-text {
  margin-bottom: 8px;
  font-size: 12px;
  font-weight: 700;
}

.plan-info-row {
  display: flex;
  margin-bottom: 6px;
}

.info-label {
  width: 80px;
  font-size: 11px;
  color: #999999;
}

.info-value {
  width: calc(100% - 80px);
  font-size: 12px;
  color: #333333;
}

.plan-footer {
  display: flex;
  width: 100%;
}

.plan-progress {
  display: inline-flex;
  width: 60%;
}

.progress-label {
  width: 65px;
  font-size: 11px;
  color: #999999;
}

.progress-value {
  width: calc(100% - 65px);
  font-size: 11px;
  color: #36b876;
}
.plan-defects {
  display: inline-flex;
  width: 40%;
}

.defects-label {
  width: 30px;
  font-size: 11px;
  color: #999999;
}

.defects-value {
  width: calc(100% - 30px);
  font-size: 11px;
  color: #f05656;
}
.plan-chart {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100px;
}
/* 圆环内容样式 */
.circle-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.circle-title {
  font-size: 10px;
  font-weight: bold;
}

.circle-percent {
  font-size: 10px;
}
</style>
