import { http } from '@/utils/http'
import { ColumnItem } from 'wot-design-uni/components/wd-picker-view/types'
import {
  IScheduleCheckParams,
  IScheduleAddParams,
  IScheduleEditParams,
  IAbnormalParams,
  IUnabnormalParams,
} from './types'

export const getUerRoleName = (roleName: string) => {
  return http.get<ColumnItem[]>('/api/v1/terminal/schedule/getUserByRoleName', { roleName })
}

export const checkSchedule = (data: IScheduleCheckParams) => {
  return http.post('/api/v1/terminal/schedule/judgeSchedule', data)
}

export const addSchedule = (data: IScheduleAddParams) => {
  return http.post('/api/v1/terminal/schedule/mobileAddSchedule', data)
}

export const editSchedule = (data: IScheduleEditParams) => {
  return http.post('/api/v1/terminal/schedule/mobileEditSchedule', data)
}

export const abnormal = (data: IAbnormalParams) => {
  return http.post('/api/v1/terminal/schedule/abnormal', data)
}

export const unabnormal = (data: IUnabnormalParams) => {
  return http.post('/api/v1/terminal/schedule/unAbnormal', data)
}
// 添加临时工
export const addTemporaryWorker = (userName: string | number) => {
  return http.post('/api/v1/terminal/schedule/addTemporaryWorker', { userName })
}
