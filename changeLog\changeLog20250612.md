# 变更日志 - 2025年6月12日

## [14:39:53] feat: 添加日期范围格式化功能

**作者**: 系统用户  
**文件**: src/pages/quality/completed-inspection/form.vue

### 变更描述

为完工检验表单添加了日期范围格式化功能，将日历组件选择的时间戳数组转换为格式化的日期对象，格式为 `{startTime: 'YYYY-MM-DD', endTime: 'YYYY-MM-DD'}`。

### 变更详情

**原代码**:

```javascript
// 处理日历确认
const handleCalendarConfirm = ({ value }) => {
  console.log('选择的日期范围:', value)
  console.log(value)
}
```

**新代码**:

```javascript
// 格式化日期的工具函数
const formatDate = (timestamp) => {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 处理日历确认
const handleCalendarConfirm = ({ value }) => {
  // 转换为指定格式
  const formattedDateRange = {
    startTime: formatDate(value[0]),
    endTime: formatDate(value[1]),
  }

  console.log('选择的日期范围:', formattedDateRange)

  // 将格式化后的日期范围保存到表单数据中
  formData.formattedDateRange = formattedDateRange
}

// 表单数据
const formData = reactive({
  // ... 其他字段
  formattedDateRange: { startTime: '', endTime: '' }, // 添加格式化后的日期范围
})

onMounted(() => {
  // 初始化格式化后的日期范围
  formData.formattedDateRange = {
    startTime: formatDate(formData.dateRange[0]),
    endTime: formatDate(formData.dateRange[1]),
  }
})
```

## [14:33:56] fix: 优化完工检验表单日期选择器显示方式

**作者**: 系统用户  
**文件**: src/pages/quality/completed-inspection/form.vue

### 变更描述

优化了完工检验表单中日期选择器的显示方式，将原来的点击弹出式日历改为直接内嵌显示的日历组件，提升了用户体验。

### 变更详情

**原代码**:

```vue
<view class="date-picker-wrapper" @click="showCalendar = true">
  <text class="date-label">检验日期范围：</text>
  <text class="date-value">{{ formatDateRange(formData.dateRange) }}</text>
  <wd-icon name="arrow-right" size="14" class="date-icon" />
  <wd-calendar
    v-model="formData.dateRange"
    type="daterange"
    :min-date="minDate"
    :max-date="maxDate"
    :visible="showCalendar"
    @confirm="handleCalendarConfirm"
    @close="showCalendar = false"
  />
</view>
```

**新代码**:

```vue
<view class="date-picker-wrapper">
  <wd-calendar
    v-model="formData.dateRange"
    type="daterange"
    :min-date="minDate"
    :max-date="maxDate"
    label="检验日期范围"
    size="large"
  />
</view>
```

## [14:02:16] fix: 修复完工检验表单日期范围选择限制问题

**作者**: 系统用户  
**文件**: src/pages/quality/completed-inspection/form.vue

### 变更描述

修复了完工检验表单中日期范围选择器的限制问题。之前的实现中，日期选择被限制在minDate和maxDate之间（仅今天和明天），现在扩大了可选范围至整个2025年，并将默认选择设置为今天和明天。

### 变更详情

**原代码**:

```javascript
// 日历选择器相关
const showCalendar = ref(false)
const minDate = new Date().getTime() // 2025-01-01
const maxDate = new Date(new Date().getTime() + 24 * 60 * 60 * 1000).getTime() // 当前时间加一天

// 表单数据
const formData = reactive({
  dateRange: [minDate, maxDate], // 默认为日期范围
  // ...其他字段
})
```

**新代码**:

```javascript
// 日历选择器相关
const showCalendar = ref(false)
// 设置日期范围：最小日期为2025年初，最大日期为2025年底
const minDate = new Date(2025, 0, 1).getTime() // 2025-01-01
const maxDate = new Date(2025, 11, 31).getTime() // 2025-12-31

// 获取今天和明天的时间戳作为默认选择
const today = new Date()
const tomorrow = new Date(today)
tomorrow.setDate(tomorrow.getDate() + 1)
const todayTimestamp = today.getTime()
const tomorrowTimestamp = tomorrow.getTime()

// 表单数据
const formData = reactive({
  dateRange: [todayTimestamp, tomorrowTimestamp], // 默认选择今天和明天
  // ...其他字段
})
```

## [13:50:58] feat: 将完工检验表单中的日期选择器改为日期范围选择

**作者**: 系统用户  
**文件**: src/pages/quality/completed-inspection/form.vue

### 变更描述

将完工检验表单中的日期选择器由单日期选择改为日期范围选择，使用wot-design-uni的wd-calendar组件，实现了日期范围选择功能。

### 变更详情

**原代码**:

```vue
<view class="date-picker-wrapper" @click="showCalendar = true">
  <text class="date-label">检验日期：</text>
  <text class="date-value">{{ formatDate(formData.inspectionDate) }}</text>
  <wd-icon name="arrow-right" size="14" class="date-icon" />
</view>
<wd-calendar
  v-model="formData.inspectionDate"
  :min-date="minDate"
  :max-date="maxDate"
  :visible="showCalendar"
  @confirm="handleCalendarConfirm"
  @close="showCalendar = false"
/>
```

**新代码**:

```vue
<view class="date-picker-wrapper" @click="showCalendar = true">
  <text class="date-label">检验日期范围：</text>
  <text class="date-value">{{ formatDateRange(formData.dateRange) }}</text>
  <wd-icon name="arrow-right" size="14" class="date-icon" />
</view>
<wd-calendar
  v-model="formData.dateRange"
  type="daterange"
  :min-date="minDate"
  :max-date="maxDate"
  :visible="showCalendar"
  @confirm="handleCalendarConfirm"
  @close="showCalendar = false"
/>
```

**原数据处理代码**:

```javascript
// 格式化日期显示
const formatDate = (timestamp) => {
  if (!timestamp) return '请选择日期'

  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')

  return `${year}-${month}-${day}`
}

// 处理日历确认
const handleCalendarConfirm = ({ value }) => {
  formData.inspectionDate = value
  showCalendar.value = false
  console.log('选择的日期:', formatDate(value))
}

// 表单数据
const formData = reactive({
  inspectionDate: Date.now(), // 默认为当前日期的时间戳
  // ...其他字段
})
```

**新数据处理代码**:

```javascript
// 格式化日期范围显示
const formatDateRange = (dateRange) => {
  if (!dateRange || !Array.isArray(dateRange) || dateRange.length < 2) {
    return '请选择日期范围'
  }

  const formatSingleDate = (timestamp) => {
    if (!timestamp) return ''

    const date = new Date(timestamp)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')

    return `${year}-${month}-${day}`
  }

  const startDate = formatSingleDate(dateRange[0])
  const endDate = formatSingleDate(dateRange[1])

  return `${startDate} 至 ${endDate}`
}

// 处理日历确认
const handleCalendarConfirm = ({ value }) => {
  formData.dateRange = value
  showCalendar.value = false
  console.log('选择的日期范围:', formatDateRange(value))
}

// 表单数据
const formData = reactive({
  dateRange: [minDate, maxDate], // 默认为日期范围
  inspectionDate: Date.now(), // 保留原有字段以兼容其他代码
  // ...其他字段
})
```
