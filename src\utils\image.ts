/**
 * 将图片列表转换为逗号分隔的字符串格式
 * 参考completed-inspection页面的实现方式
 * @param fileList - 图片文件列表
 * @returns 逗号分隔的图片名称字符串
 */
const convertImageListToString = (fileList: any[]): string => {
  // 从imageList中提取图片名称，支持多种响应格式
  const imageUrls = fileList
    .map((file: any) => {
      // 尝试从不同的属性中获取图片名称
      return JSON.parse(file.response).realName
    })
    .filter(Boolean) // 过滤掉空值

  return imageUrls.join(',') // 用逗号连接多张图片的路径
}

export { convertImageListToString }
