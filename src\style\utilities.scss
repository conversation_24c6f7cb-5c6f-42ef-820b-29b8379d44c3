/**
 * S-MES移动端工具类样式
 * 常用的原子化CSS工具类
 * 作者: AI Assistant
 * 创建时间: 2025-06-18
 */

@import './design-system.scss';

/* ==================== 间距工具类 ==================== */

// 外边距 (margin)
.m-0 { margin: 0; }
.m-xs { margin: $spacing-xs; }
.m-sm { margin: $spacing-sm; }
.m-md { margin: $spacing-md; }
.m-lg { margin: $spacing-lg; }
.m-xl { margin: $spacing-xl; }
.m-xxl { margin: $spacing-xxl; }

// 水平外边距
.mx-0 { margin-left: 0; margin-right: 0; }
.mx-xs { margin-left: $spacing-xs; margin-right: $spacing-xs; }
.mx-sm { margin-left: $spacing-sm; margin-right: $spacing-sm; }
.mx-md { margin-left: $spacing-md; margin-right: $spacing-md; }
.mx-lg { margin-left: $spacing-lg; margin-right: $spacing-lg; }
.mx-xl { margin-left: $spacing-xl; margin-right: $spacing-xl; }

// 垂直外边距
.my-0 { margin-top: 0; margin-bottom: 0; }
.my-xs { margin-top: $spacing-xs; margin-bottom: $spacing-xs; }
.my-sm { margin-top: $spacing-sm; margin-bottom: $spacing-sm; }
.my-md { margin-top: $spacing-md; margin-bottom: $spacing-md; }
.my-lg { margin-top: $spacing-lg; margin-bottom: $spacing-lg; }
.my-xl { margin-top: $spacing-xl; margin-bottom: $spacing-xl; }

// 单方向外边距
.mt-0 { margin-top: 0; }
.mt-xs { margin-top: $spacing-xs; }
.mt-sm { margin-top: $spacing-sm; }
.mt-md { margin-top: $spacing-md; }
.mt-lg { margin-top: $spacing-lg; }
.mt-xl { margin-top: $spacing-xl; }

.mr-0 { margin-right: 0; }
.mr-xs { margin-right: $spacing-xs; }
.mr-sm { margin-right: $spacing-sm; }
.mr-md { margin-right: $spacing-md; }
.mr-lg { margin-right: $spacing-lg; }
.mr-xl { margin-right: $spacing-xl; }

.mb-0 { margin-bottom: 0; }
.mb-xs { margin-bottom: $spacing-xs; }
.mb-sm { margin-bottom: $spacing-sm; }
.mb-md { margin-bottom: $spacing-md; }
.mb-lg { margin-bottom: $spacing-lg; }
.mb-xl { margin-bottom: $spacing-xl; }

.ml-0 { margin-left: 0; }
.ml-xs { margin-left: $spacing-xs; }
.ml-sm { margin-left: $spacing-sm; }
.ml-md { margin-left: $spacing-md; }
.ml-lg { margin-left: $spacing-lg; }
.ml-xl { margin-left: $spacing-xl; }

// 内边距 (padding)
.p-0 { padding: 0; }
.p-xs { padding: $spacing-xs; }
.p-sm { padding: $spacing-sm; }
.p-md { padding: $spacing-md; }
.p-lg { padding: $spacing-lg; }
.p-xl { padding: $spacing-xl; }
.p-xxl { padding: $spacing-xxl; }

// 水平内边距
.px-0 { padding-left: 0; padding-right: 0; }
.px-xs { padding-left: $spacing-xs; padding-right: $spacing-xs; }
.px-sm { padding-left: $spacing-sm; padding-right: $spacing-sm; }
.px-md { padding-left: $spacing-md; padding-right: $spacing-md; }
.px-lg { padding-left: $spacing-lg; padding-right: $spacing-lg; }
.px-xl { padding-left: $spacing-xl; padding-right: $spacing-xl; }

// 垂直内边距
.py-0 { padding-top: 0; padding-bottom: 0; }
.py-xs { padding-top: $spacing-xs; padding-bottom: $spacing-xs; }
.py-sm { padding-top: $spacing-sm; padding-bottom: $spacing-sm; }
.py-md { padding-top: $spacing-md; padding-bottom: $spacing-md; }
.py-lg { padding-top: $spacing-lg; padding-bottom: $spacing-lg; }
.py-xl { padding-top: $spacing-xl; padding-bottom: $spacing-xl; }

// 单方向内边距
.pt-0 { padding-top: 0; }
.pt-xs { padding-top: $spacing-xs; }
.pt-sm { padding-top: $spacing-sm; }
.pt-md { padding-top: $spacing-md; }
.pt-lg { padding-top: $spacing-lg; }
.pt-xl { padding-top: $spacing-xl; }

.pr-0 { padding-right: 0; }
.pr-xs { padding-right: $spacing-xs; }
.pr-sm { padding-right: $spacing-sm; }
.pr-md { padding-right: $spacing-md; }
.pr-lg { padding-right: $spacing-lg; }
.pr-xl { padding-right: $spacing-xl; }

.pb-0 { padding-bottom: 0; }
.pb-xs { padding-bottom: $spacing-xs; }
.pb-sm { padding-bottom: $spacing-sm; }
.pb-md { padding-bottom: $spacing-md; }
.pb-lg { padding-bottom: $spacing-lg; }
.pb-xl { padding-bottom: $spacing-xl; }

.pl-0 { padding-left: 0; }
.pl-xs { padding-left: $spacing-xs; }
.pl-sm { padding-left: $spacing-sm; }
.pl-md { padding-left: $spacing-md; }
.pl-lg { padding-left: $spacing-lg; }
.pl-xl { padding-left: $spacing-xl; }

/* ==================== 文本工具类 ==================== */

// 文本对齐
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

// 文本大小
.text-xs { font-size: $font-size-xs; }
.text-sm { font-size: $font-size-sm; }
.text-base { font-size: $font-size-base; }
.text-lg { font-size: $font-size-lg; }
.text-xl { font-size: $font-size-xl; }
.text-xxl { font-size: $font-size-xxl; }

// 文本粗细
.font-light { font-weight: $font-weight-light; }
.font-normal { font-weight: $font-weight-normal; }
.font-medium { font-weight: $font-weight-medium; }
.font-bold { font-weight: $font-weight-bold; }

// 文本颜色
.text-primary { color: $text-primary; }
.text-secondary { color: $text-secondary; }
.text-tertiary { color: $text-tertiary; }
.text-placeholder { color: $text-placeholder; }
.text-disabled { color: $text-disabled; }
.text-inverse { color: $text-inverse; }

// 主题颜色文本
.text-theme { color: $primary-color; }
.text-success { color: $success-color; }
.text-warning { color: $warning-color; }
.text-error { color: $error-color; }
.text-info { color: $info-color; }

// 文本装饰
.text-underline { text-decoration: underline; }
.text-line-through { text-decoration: line-through; }
.text-no-decoration { text-decoration: none; }

// 文本换行
.text-nowrap { white-space: nowrap; }
.text-wrap { white-space: normal; }
.text-break { word-break: break-all; }

// 文本省略
.text-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.text-ellipsis-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* ==================== 背景工具类 ==================== */

// 背景颜色
.bg-page { background-color: $bg-page; }
.bg-card { background-color: $bg-card; }
.bg-input { background-color: $bg-input; }
.bg-hover { background-color: $bg-hover; }

// 主题背景色
.bg-primary { background-color: $primary-color; }
.bg-success { background-color: $success-color; }
.bg-warning { background-color: $warning-color; }
.bg-error { background-color: $error-color; }
.bg-info { background-color: $info-color; }

// 透明背景
.bg-transparent { background-color: transparent; }

/* ==================== 边框工具类 ==================== */

// 边框
.border { border: 1rpx solid $border-light; }
.border-0 { border: none; }
.border-t { border-top: 1rpx solid $border-light; }
.border-r { border-right: 1rpx solid $border-light; }
.border-b { border-bottom: 1rpx solid $border-light; }
.border-l { border-left: 1rpx solid $border-light; }

// 边框颜色
.border-light { border-color: $border-light; }
.border-base { border-color: $border-base; }
.border-dark { border-color: $border-dark; }
.border-primary { border-color: $primary-color; }
.border-success { border-color: $success-color; }
.border-warning { border-color: $warning-color; }
.border-error { border-color: $error-color; }

// 圆角
.rounded-none { border-radius: 0; }
.rounded-xs { border-radius: $border-radius-xs; }
.rounded-sm { border-radius: $border-radius-sm; }
.rounded { border-radius: $border-radius-base; }
.rounded-lg { border-radius: $border-radius-lg; }
.rounded-xl { border-radius: $border-radius-xl; }
.rounded-full { border-radius: $border-radius-circle; }

/* ==================== 显示工具类 ==================== */

// 显示类型
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }
.hidden { display: none; }

// 可见性
.visible { visibility: visible; }
.invisible { visibility: hidden; }

// 透明度
.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

/* ==================== 定位工具类 ==================== */

// 定位类型
.static { position: static; }
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

// 层级
.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }

/* ==================== 尺寸工具类 ==================== */

// 宽度
.w-full { width: 100%; }
.w-auto { width: auto; }
.w-0 { width: 0; }

// 高度
.h-full { height: 100%; }
.h-auto { height: auto; }
.h-0 { height: 0; }
.h-screen { height: 100vh; }

// 最小/最大尺寸
.min-h-screen { min-height: 100vh; }
.max-w-full { max-width: 100%; }
.max-h-full { max-height: 100%; }

/* ==================== 阴影工具类 ==================== */

.shadow-none { box-shadow: none; }
.shadow-light { box-shadow: $shadow-light; }
.shadow { box-shadow: $shadow-base; }
.shadow-dark { box-shadow: $shadow-dark; }

/* ==================== 过渡动画工具类 ==================== */

.transition { transition: all $duration-base $ease-in-out; }
.transition-fast { transition: all $duration-fast $ease-in-out; }
.transition-slow { transition: all $duration-slow $ease-in-out; }

/* ==================== 交互状态工具类 ==================== */

// 鼠标指针
.cursor-pointer { cursor: pointer; }
.cursor-default { cursor: default; }
.cursor-not-allowed { cursor: not-allowed; }

// 用户选择
.select-none { user-select: none; }
.select-text { user-select: text; }
.select-all { user-select: all; }

// 触摸操作
.touch-auto { touch-action: auto; }
.touch-none { touch-action: none; }
.touch-pan-x { touch-action: pan-x; }
.touch-pan-y { touch-action: pan-y; }
