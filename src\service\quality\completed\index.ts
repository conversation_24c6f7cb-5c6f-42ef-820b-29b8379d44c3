import { http } from '@/utils/http'

export const getQualityList = (params: any) => {
  return http.get('/api/v1/terminal/final-quality', params)
}

export const updateQuality = (data: any) => {
  return http.put('/api/v1/terminal/final-quality', data)
}

export const getQualityDetail = (params: any) => {
  return http.get('/api/v1/terminal/final-quality/detail', params)
}

// 新增
export const addQuality = (data: ICompletedForm) => {
  return http.post('/api/v1/terminal/final-quality', data)
}

// 获取取证备注图片
export const getQualityImages = (params: any) => {
  return http.get('/api/localStorage/files/', params)
}
