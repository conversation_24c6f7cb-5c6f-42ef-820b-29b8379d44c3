/**
 * S-MES移动端设计系统
 * 统一的设计token和基础样式定义
 * 作者: AI Assistant
 * 创建时间: 2025-06-18
 */

/* ==================== 颜色系统 ==================== */

// 主色调系统
$primary-color: #2979ff;           // 主色调 - 蓝色
$primary-light: #5a9cff;          // 主色调浅色
$primary-dark: #1565c0;           // 主色调深色

// 功能色系统
$success-color: #19be6b;          // 成功色 - 绿色
$warning-color: #ff9900;          // 警告色 - 橙色
$error-color: #fa3534;            // 错误色 - 红色
$info-color: #909399;             // 信息色 - 灰色

// 文本颜色系统
$text-primary: #333333;           // 主要文本颜色
$text-secondary: #666666;         // 次要文本颜色
$text-tertiary: #999999;          // 辅助文本颜色
$text-placeholder: #c0c4cc;       // 占位符文本颜色
$text-disabled: #c0c0c0;          // 禁用文本颜色
$text-inverse: #ffffff;           // 反色文本（白色）

// 背景颜色系统
$bg-page: #f5f5f5;               // 页面背景色
$bg-card: #ffffff;               // 卡片背景色
$bg-input: #f8f8f8;              // 输入框背景色
$bg-hover: #f1f1f1;              // 悬停背景色
$bg-mask: rgba(0, 0, 0, 0.4);    // 遮罩背景色

// 边框颜色系统
$border-light: #ebedf0;          // 浅色边框
$border-base: #dcdfe6;           // 基础边框
$border-dark: #c0c4cc;           // 深色边框

/* ==================== 间距系统 ==================== */

// 基础间距单位（8rpx为基础单位）
$spacing-base: 8rpx;

// 间距规范
$spacing-xs: $spacing-base;       // 8rpx - 极小间距
$spacing-sm: $spacing-base * 2;   // 16rpx - 小间距
$spacing-md: $spacing-base * 3;   // 24rpx - 中等间距
$spacing-lg: $spacing-base * 4;   // 32rpx - 大间距
$spacing-xl: $spacing-base * 5;   // 40rpx - 超大间距
$spacing-xxl: $spacing-base * 6;  // 48rpx - 极大间距

// 页面布局间距
$page-padding: $spacing-lg;       // 页面内边距 32rpx
$section-margin: $spacing-md;     // 区块间距 24rpx
$content-padding: $spacing-md;    // 内容内边距 24rpx
$element-margin: $spacing-sm;     // 元素间距 16rpx

/* ==================== 字体系统 ==================== */

// 字体大小
$font-size-xs: 20rpx;            // 极小字体
$font-size-sm: 24rpx;            // 小字体
$font-size-base: 28rpx;          // 基础字体
$font-size-lg: 32rpx;            // 大字体
$font-size-xl: 36rpx;            // 超大字体
$font-size-xxl: 40rpx;           // 极大字体

// 字体粗细
$font-weight-light: 300;         // 细体
$font-weight-normal: 400;        // 正常
$font-weight-medium: 500;        // 中等
$font-weight-bold: 600;          // 粗体

// 行高
$line-height-tight: 1.2;         // 紧凑行高
$line-height-base: 1.4;          // 基础行高
$line-height-loose: 1.6;         // 宽松行高

/* ==================== 圆角系统 ==================== */

$border-radius-xs: 2rpx;         // 极小圆角
$border-radius-sm: 4rpx;         // 小圆角
$border-radius-base: 8rpx;       // 基础圆角
$border-radius-lg: 12rpx;        // 大圆角
$border-radius-xl: 16rpx;        // 超大圆角
$border-radius-circle: 50%;      // 圆形

/* ==================== 阴影系统 ==================== */

$shadow-light: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);     // 浅阴影
$shadow-base: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);       // 基础阴影
$shadow-dark: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);     // 深阴影

/* ==================== Z-index层级系统 ==================== */

$z-index-dropdown: 1000;         // 下拉菜单
$z-index-sticky: 1020;           // 粘性定位
$z-index-fixed: 1030;            // 固定定位
$z-index-modal-backdrop: 1040;   // 模态框背景
$z-index-modal: 1050;            // 模态框
$z-index-popover: 1060;          // 弹出框
$z-index-tooltip: 1070;          // 工具提示
$z-index-toast: 1080;            // 消息提示

/* ==================== 动画系统 ==================== */

// 动画时长
$duration-fast: 0.15s;           // 快速动画
$duration-base: 0.3s;            // 基础动画
$duration-slow: 0.5s;            // 慢速动画

// 动画缓动函数
$ease-out: cubic-bezier(0.25, 0.46, 0.45, 0.94);
$ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);
$ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);

/* ==================== 断点系统 ==================== */

$breakpoint-sm: 576px;           // 小屏幕
$breakpoint-md: 768px;           // 中等屏幕
$breakpoint-lg: 992px;           // 大屏幕
$breakpoint-xl: 1200px;          // 超大屏幕

/* ==================== 质量检验模块专用颜色 ==================== */

// 质量检验状态颜色
$quality-pass: $success-color;    // 合格状态
$quality-fail: $error-color;     // 不合格状态
$quality-pending: $warning-color; // 待检验状态
$quality-primary: $primary-color; // 质检模块主色

/* ==================== CSS变量定义（兼容wot-design-uni） ==================== */

:root {
  // 主题色覆盖
  --wot-color-theme: #{$primary-color};
  
  // 背景色
  --wot-bg-color: #{$bg-card};
  --wot-bg-color-page: #{$bg-page};
  
  // 文本色
  --wot-color-text: #{$text-primary};
  --wot-color-text-secondary: #{$text-secondary};
  
  // 边框色
  --wot-border-color: #{$border-light};
  
  // 按钮色
  --wot-button-primary-bg-color: #{$primary-color};
  --wot-button-success-bg-color: #{$success-color};
  --wot-button-warning-bg-color: #{$warning-color};
  --wot-button-error-bg-color: #{$error-color};
}
