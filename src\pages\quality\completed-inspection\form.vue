<route lang="json5">
{
  needLogin: true,
  style: {
    navigationBarTitleText: '质检',
  },
}
</route>

<template>
  <view class="container">
    <!-- 顶部日期选择器 -->
    <view class="date-selector">
      <view class="date-picker-wrapper">
        <wd-calendar
          allow-same-day
          v-model="formData.dateRange"
          type="daterange"
          :min-date="minDate"
          :max-date="maxDate"
          label="检验日期范围"
          size="large"
          @confirm="handleCalendarConfirm"
        />
      </view>
    </view>

    <wd-form ref="formRef" :model="formData" :rules="formRules">
      <!-- 生产机台选择 - 使用动态任务列表，与老项目逻辑一致 -->
      <view class="production-line-section">
        <view class="production-line-row">
          <text class="production-line-label">生产机台：</text>
          <picker
            @change="handleProductionLineChange"
            :value="machinePlan.taskId"
            :range="dayTaskList"
            range-key="name"
            class="production-line-picker"
          >
            <view class="picker-content">
              <text class="picker-text">
                {{
                  dayTaskList.length > 0
                    ? dayTaskList[machinePlan.taskId]?.name || '暂无数据'
                    : '暂无数据'
                }}
              </text>
              <view class="picker-arrow">
                <view class="arrow-down"></view>
              </view>
            </view>
          </picker>
        </view>
      </view>

      <!-- 工单信息卡片 -->
      <view v-if="showWorkOrderInfo" class="info-card">
        <view class="card-header">
          <view class="header-line"></view>
          <text class="card-title">工单信息</text>
        </view>
        <view class="info-list">
          <view class="info-row">
            <text class="info-label">生产机台</text>
            <text class="info-value">{{ workOrderInfo.productionLine }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">排程时间</text>
            <text class="info-value">{{ workOrderInfo.scheduleDate }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">加工单</text>
            <text class="info-value">{{ workOrderInfo.orderNum }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">计划数量</text>
            <text class="info-value">{{ workOrderInfo.planQuantity }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">预入库数量</text>
            <text class="info-value">{{ workOrderInfo.estimatedReceiptQuantity }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">班次</text>
            <text class="info-value">{{ workOrderInfo.shift === 0 ? '白班' : '夜班' }}</text>
          </view>
        </view>
      </view>

      <!-- 质检类型卡片 -->
      <view class="info-card">
        <view class="card-header">
          <view class="header-line"></view>
          <text class="card-title">质检类型</text>
          <view class="type-badge">全检</view>
        </view>

        <!-- 检验条件展示 -->
        <view class="form-item">
          <view class="form-row">
            <text class="form-label">检验条件：</text>
          </view>
          <view class="display-box">
            <text class="display-text">{{ qualityInfoData.condition || '—' }}</text>
          </view>
        </view>

        <!-- 备注展示 -->
        <view class="form-item">
          <text class="form-label">备注：</text>
          <view class="display-box remark-box">
            <text class="display-text">{{ qualityInfoData.qualityRemark || '—' }}</text>
          </view>
        </view>

        <!-- 质检项选择 - 从老项目移植的核心功能 -->
        <view class="form-item">
          <view class="quality-items-container">
            <view class="quality-items-header">
              <text class="form-label">质检项：</text>
              <!-- 文件预览按钮 - 当存在检验图片或文件时显示 -->
              <view class="file-preview-btn" @tap="openFile" v-if="qualityInfoData.exPic">
                <wd-icon name="folder" class="file-icon" />
              </view>
            </view>
            <view class="quality-items-scroll">
              <checkbox-group @change="changeCheckbox">
                <view
                  class="quality-item"
                  v-for="(item, index) in qualityInfoData.qualityItem"
                  :key="index"
                >
                  <view class="checkbox-container">
                    <checkbox
                      :value="String(item.id)"
                      :checked="item.checked"
                      :disabled="isFormDisabled"
                      class="quality-checkbox"
                    />
                  </view>
                  <view class="quality-item-content">
                    <view class="quality-item-title">
                      {{ item.title }}
                    </view>
                    <view class="quality-item-detail">
                      <view class="detail-row">
                        <text class="detail-label">检验工具:</text>
                        <text class="detail-value">{{ toolList[item.tool] || '—' }}</text>
                      </view>
                      <view class="detail-row">
                        <text class="detail-label">质检标准:</text>
                        <text class="detail-value">{{ item.standrad || '—' }}</text>
                      </view>
                    </view>
                  </view>
                </view>
              </checkbox-group>
            </view>
          </view>
        </view>

        <!-- 批次码 -->
        <!-- <view class="form-item">
          <text class="form-label">批次码：</text>
          <view class="input-with-scan">
            <wd-input v-model="formData.batchNo" placeholder="请扫描批次码" class="scan-input" />
            <view class="scan-btn" @tap="scanBatchNo">
              <wd-icon name="scan" class="scan-icon" />
            </view>
          </view>
        </view> -->

        <!-- 不良品数量 -->
        <view class="form-item">
          <text class="form-label required">*不良品数量：</text>
          <wd-input
            v-model="formData.defQuantity"
            type="number"
            placeholder="请输入"
            class="form-input"
            :min="0"
          />
        </view>

        <!-- 取证备注 -->
        <view class="form-item">
          <text class="form-label">取证备注：</text>
          <view class="upload-container">
            <ImageUpload
              action="/api/localStorage/pictures"
              v-model:fileList="imageList"
              :max-count="6"
              :before-upload="handleBeforeUpload"
              @upload-success="onUploadSuccess"
            />
          </view>
        </view>

        <!-- 备注 -->
        <view class="form-item">
          <text class="form-label">备注：</text>
          <wd-textarea
            v-model="formData.remark"
            placeholder="请输入"
            :maxlength="200"
            show-word-count
            class="form-textarea"
          />
        </view>
      </view>

      <!-- 提交按钮 -->
      <view class="submit-container">
        <wd-button type="primary" block @click="handleSubmit" class="submit-btn">提交</wd-button>
      </view>
    </wd-form>

    <!-- 文件预览抽屉 -->
    <wd-popup v-model="showDrawer" position="right" :width="'80%'">
      <view class="drawer-content">
        <view class="drawer-header">
          <text class="drawer-title">文件预览</text>
          <wd-button type="text" @click="showDrawer = false">关闭</wd-button>
        </view>
        <view class="drawer-body">
          <view v-if="imgList.length > 0" class="preview-section">
            <text class="section-title" @tap="jumpImage">点此预览图片</text>
          </view>
          <view v-if="fileList.length > 0" class="preview-section">
            <view v-for="(file, index) in fileList" :key="index" class="file-item">
              <text class="file-name" @tap="jumpFile(file)">{{ file }}</text>
            </view>
          </view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useUserStore } from '@/store/user'
import * as qualityCompletedApi from '@/service/quality/completed'
import ImageUpload from '@/components/common/ImageUpload.vue'
import { useToast } from 'wot-design-uni'
// 引入字典API，使用老项目的接口保持兼容性
import * as dictApi from '@/service/dict'
// 引入任务API，从老项目移植的接口
import * as taskApi from '@/service/task'
// 表单引用
const formRef = ref()

// 格式化日期的工具函数
const formatDate = (timestamp: number) => {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 获取用户信息
const userStore = useUserStore()

// 是否显示工单信息卡片
const showWorkOrderInfo = ref(false)

// 设置日期范围：最小日期为当年初，最大日期为当年底
const minDate = new Date(new Date().getFullYear(), 0, 1).getTime()
const maxDate = new Date(new Date().getFullYear(), 11, 31).getTime()

// 获取今天和明天的时间戳作为默认选择
const today = new Date()
const tomorrow = new Date(today)
const todayTimestamp = today.getTime()
const tomorrowTimestamp = tomorrow.getTime()

// 处理日历确认 - 增强版本，支持重新获取任务列表
const handleCalendarConfirm = ({ value }: { value: number[] }) => {
  // 更新日期范围
  formData.dateRange = value

  // 转换为指定格式
  const formattedDateRange = {
    startTime: formatDate(value[0]) + ' ' + '00:00:00',
    endTime: formatDate(value[1]) + ' ' + '23:59:59',
  }

  console.log('选择的日期范围:', formattedDateRange)

  // 将格式化后的日期范围保存到表单数据中
  formData.formattedDateRange = formattedDateRange

  // 时间更改后重新获取任务列表，与老项目逻辑保持一致
  getDayTaskList()
}

// 生产机台选项 - 已改为使用动态任务列表，保留以备后用
// const productionLineOptions = [
//   { value: '智能数控', label: '智能数控' },
//   { value: '精密铣床', label: '精密铣床' },
//   { value: '压铸机A线', label: '压铸机A线' },
//   { value: '压铸机B线', label: '压铸机B线' },
//   { value: '组装线', label: '组装线' },
// ]

// 生产机台名称数组(用于picker) - 已改为使用dayTaskList
// const productionLineNames = productionLineOptions.map((item) => item.label)
// 当前选中的索引 - 已改为使用machinePlan.taskId
// const productionLineIndex = ref(0)

// 班次选项（保留以备后用）
// const shiftOptions = [
//   { value: '早班', label: '早班' },
//   { value: '中班', label: '中班' },
//   { value: '晚班', label: '晚班' },
// ]

// 工单信息数据
const workOrderInfo = reactive({
  productionLine: '',
  scheduleDate: '',
  orderNum: '',
  planQuantity: Number,
  estimatedReceiptQuantity: Number,
  shift: Number,
})
// 质检条件信息
const qualityInfoData = reactive({
  exPic: '', // 检验图片或文件名
  condition: '', // 检验条件
  forRemark: [], // 取证备注（数组）
  isComplete: false, // 是否完成
  qualityRemark: '', // 质检备注
  qualityItem: [], // 质检项（数组）
})

// 检验工具字典
const toolList = ref<Record<string, string>>({})

// 表单是否禁用
const isFormDisabled = ref(false)

// 文件预览相关
const imgList = ref<string[]>([])
const fileList = ref<string[]>([])
const showDrawer = ref(false)

// 任务列表相关数据 - 从老项目移植
const dayTaskList = ref<
  Array<{
    id: number
    name: string
    reportId: number
  }>
>([])

// 机台计划相关
const machinePlan = reactive({
  taskId: 0, // 当前选中的任务索引
})

// 用户数据 - 使用新项目的用户store，不再需要单独的userData变量
// const userData = ref<any>(null)

// work: {
//         // 报工记录id
//         workReportId: null,
//         // 不良品数量,
//         defQuantity: null,
//         // 质检项,
//         qualityItem: null,
//         // 取证备注,
//         forRemark: null,
//         // 备注,
//         remark: null,
//         // 质检结果（0-通过，1-返工）
//         result: null,
//         // 返工原因(非必填)
//         reason: null,
//         // 图片列表
//         imageValueList: [],
// 表单数据
const formData = reactive({
  dateRange: [todayTimestamp, tomorrowTimestamp], // 默认选择今天和明天
  inspectionDate: Date.now(), // 保留原有字段以兼容其他代码
  inspector: userStore.userInfo?.user?.nickName || '系统用户',
  productionLine: '',
  inspectionType: '全检', // 固定为全检
  batchNo: '',
  workReportId: null,
  // 不良品数量,
  defQuantity: null,
  // 质检项,
  qualityItem: null,
  // 取证备注,
  forRemark: null,
  // 备注,
  remark: null,
  // 质检结果（0-通过，1-返工）
  result: 0,
  // 返工原因(非必填)
  reason: null,
  // 图片列表
  imageValueList: [],
  fileList: [],
  formattedDateRange: { startTime: '', endTime: '' }, // 添加格式化后的日期范围
})

// 表单验证规则
const formRules: any = {
  productionLine: [{ required: true, message: '请选择生产机台', trigger: 'blur' }],
  inspectionCondition: [{ required: true, message: '请输入检验条件', trigger: 'blur' }],
  batchNo: [{ required: true, message: '请输入批次码', trigger: 'blur' }],
  defectiveQuantity: [
    {
      required: false,
      type: 'number',
      message: '不良品数量必须为数字',
      trigger: 'blur',
      transform: (value: any) => Number(value),
    },
    {
      validator: (_rule: any, value: any, callback: any) => {
        const quantity = Number(workOrderInfo.estimatedReceiptQuantity)
        const defective = Number(value)
        if (defective < 0) {
          callback(new Error('数量不能为负数'))
        } else if (defective > quantity) {
          callback(new Error('不良品数量不能大于预入库数量'))
        } else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
}

// 处理生产机台变化 - 从老项目移植的pickPlanChange逻辑，适配Vue3语法
const handleProductionLineChange = (e: any) => {
  console.log('选择的任务索引:', e.detail.value, '任务列表:', dayTaskList.value)

  // 更新选中的任务索引
  machinePlan.taskId = e.detail.value

  // 获取选中的任务
  if (dayTaskList.value.length > machinePlan.taskId) {
    const selectedTask = dayTaskList.value[machinePlan.taskId]

    // 更新表单中的生产机台信息
    formData.productionLine = selectedTask.name

    // 如果任务ID不为0，则获取质检详情
    if (selectedTask.id !== 0) {
      getInfoByQualityTaskId(selectedTask.id)
    }
  }
}

const toast = useToast()
const imageList = ref([])

// 上传前校验
const handleBeforeUpload = ({
  files,
  resolve,
}: {
  files: any[]
  resolve: (result: boolean) => void
}) => {
  // files 是一个数组，包含了本次选择的所有待上传文件
  for (const file of files) {
    // 校验单个文件的大小
    if (file.size > 4 * 1024 * 1024) {
      // 提示更具体的文件名
      toast.error(`图片 "${file.name}" 大小不能超过 2MB`)

      // 调用 resolve(false) 来阻止本次上传操作
      resolve(false)

      // 一旦有文件不合格，立刻停止后续检查
      return
    }

    // 可以在这里添加其他校验，比如文件类型
    // if (!file.type.startsWith('image/')) {
    //   toast.error(`"${file.name}" 不是一个有效的图片文件`);
    //   resolve(false);
    //   return;
    // }
  }

  // 如果所有文件都通过了校验，调用 resolve(true) 允许上传
  resolve(true)
}

// 图片上传成功回调 - 保持与现有图片上传组件的兼容性
const onUploadSuccess = ({ file: _file, fileList }: { file: any; fileList: any[] }) => {
  console.log('父组件监听到上传成功，最新的文件列表:', fileList)
  // v-model 已经处理了 imageList 的更新，这里不需要再手动操作
  // 您可以在这里触发其他业务，比如保存表单等
}

// 扫描批次码
const scanBatchNo = () => {
  console.log('扫描批次码')
  uni.scanCode({
    success: (res) => {
      formData.batchNo = res.result
    },
  })
}

// 质检项选择变化处理 - 当用户选择或取消选择质检项时触发
const changeCheckbox = (val: any) => {
  formData.qualityItem = null
  if (val.detail.value.length > 0) {
    // 将选中的质检项ID用逗号连接成字符串，与老项目保持一致
    formData.qualityItem = val.detail.value.join(',')
  }
}

// 获取检验工具字典数据 - 从老项目移植，用于显示质检项的检验工具名称
const getDictData = async () => {
  try {
    const res = await dictApi.getNametFindDict({
      blurry: 'com_tool', // 查询检验工具字典，与老项目保持一致
    })
    console.log(res, 'res dict')
    if (res.content.length > 0) {
      toolList.value = {}
      console.log(res, 'res dict###')
      // 将字典数据转换为键值对映射，便于在模板中使用
      res.content[0].dictDetails.forEach((ele) => {
        console.log(ele, 'ele')
        toolList.value[ele.value] = ele.label
      })
    }
    console.log(toolList.value, 'toolList')
  } catch (error) {
    console.error('获取字典数据失败:', error)
  }
}

// 打开文件预览
const openFile = () => {
  if (qualityInfoData.exPic) {
    const imgType = ['img', 'JPG', 'jpg', 'IMG', 'jpeg', 'png', 'JPEG', 'PNG', 'image']
    const list = qualityInfoData.exPic.split(',')
    imgList.value = []
    fileList.value = []

    list.forEach((ele) => {
      const fileArr = ele.split('.')
      // 判断当前文件是否为图片
      const flag = imgType.findIndex((item) => item === fileArr[fileArr.length - 1])
      if (flag === -1) {
        fileList.value.push(ele)
      } else {
        imgList.value.push(ele)
      }
    })

    // 当没有pdf文件时，只预览图片不打开抽屉
    if (fileList.value.length === 0) {
      jumpImage()
    } else {
      showDrawer.value = true
    }
  }
}

// 预览图片
const jumpImage = () => {
  const imgsArray: string[] = []
  if (imgList.value.length > 0) {
    imgList.value.forEach((ele) => {
      // 这里需要根据实际的图片服务器地址进行调整
      imgsArray.push(`/api/file/图片/${ele}`)
    })
  }
  uni.previewImage({
    current: 0,
    urls: imgsArray,
  })
}

// 预览文件
const jumpFile = (file: string) => {
  uni.showLoading({
    title: '打开文档中',
  })
  uni.downloadFile({
    url: `/api/file/文档/${file}`,
    success: function (res) {
      const filePath = res.tempFilePath
      uni.openDocument({
        filePath,
        showMenu: true,
        success: function () {
          uni.hideLoading()
        },
        fail: function () {
          uni.hideLoading()
          uni.showToast({
            icon: 'error',
            title: '文件打开失败',
            duration: 2000,
          })
        },
      })
    },
  })
}

// 提交前数据校验 - 从老项目移植的业务逻辑校验
const checkQualityData = async (): Promise<boolean> => {
  // 校验不良品数量不能大于预入库数量
  if (formData.defQuantity && workOrderInfo.estimatedReceiptQuantity) {
    if (Number(formData.defQuantity) > Number(workOrderInfo.estimatedReceiptQuantity)) {
      uni.showToast({
        title: '不良品数量不能大于预入库数量',
        icon: 'none',
        duration: 3000,
      })
      return false
    }
  }

  // 确认提交对话框
  return new Promise((resolve) => {
    uni.showModal({
      title: '提示',
      content: '确定提交',
      success: function (res) {
        if (res.confirm) {
          resolve(true)
        } else {
          resolve(false)
        }
      },
    })
  })
}

// 获取今日任务列表 - 从老项目移植的核心方法，适配Vue3语法和新项目的用户store
const getDayTaskList = async (qualityData?: any) => {
  try {
    // 获取用户数据 - 使用新项目的用户store
    if (!userStore.userInfo || !userStore.userInfo.user) {
      uni.showToast({
        title: '用户信息获取失败，请重新登录',
        icon: 'none',
        duration: 2000,
      })
      return
    }

    // 构建请求参数，与老项目保持一致
    const requestData = {
      taskType: 4, // 质检任务类型
      userId: userStore.userInfo.user.id,
      startTime: formData.formattedDateRange.startTime,
      endTime: formData.formattedDateRange.endTime,
    }

    const res = await taskApi.getList(requestData)
    console.log(res, 'res taskList')

    if (!res || !res.content) {
      return false
    }

    // 处理返回的任务列表数据
    if (res.content && res.content.length > 0) {
      handleToDayTask(res.content, qualityData)
    } else if (qualityData) {
      // 如果后端返回的列表为空，但有路由传参，则直接添加
      dayTaskList.value.push({
        id: qualityData.id,
        name: qualityData.machineNumber,
        reportId: qualityData.reportId,
      })
      machinePlan.taskId = dayTaskList.value.length - 1
    } else {
      // 没有数据时的处理
      dayTaskList.value = [
        {
          id: 0,
          name: '暂无数据',
          reportId: 0,
        },
      ]

      // 重置相关数据
      Object.assign(workOrderInfo, {
        device: null,
        planQuantity: null,
        estimatedReceiptQuantity: null,
        status: 0,
      })

      qualityInfoData.qualityItem = []
      formInitialization()
    }
  } catch (error) {
    console.error('获取任务列表失败:', error)
    uni.showToast({
      title: '获取任务列表失败',
      icon: 'none',
      duration: 2000,
    })
  }
}

// 处理后端返回的今日任务 - 从老项目移植，适配Vue3语法
const handleToDayTask = (taskList: any[], qualityData?: any) => {
  dayTaskList.value = []
  console.log(taskList, 'taskList')
  console.log(qualityData, 'qualityData')

  // 处理任务列表数据
  taskList.forEach((ele) => {
    dayTaskList.value.push({
      id: ele.id,
      name: ele.device.machineNumber,
      reportId: ele.reportId,
    })
  })

  // 如果qualityData为false，则需要根据设备机台id获取质检的相关数据
  if (!qualityData) {
    if (dayTaskList.value.length > 0) {
      // 使用任务ID调用getInfoByQualityTaskId，与老项目逻辑保持一致
      getInfoByQualityTaskId(dayTaskList.value[machinePlan.taskId].id)
    }
  } else {
    // 如果为true，则需要判断qualityData是否存在与后端返回的列表中
    const flag = taskList.findIndex((item) => item.id === qualityData.id)

    // flag = -1，说明qualityData不存在与后端返回的列表中
    if (flag === -1) {
      dayTaskList.value.push({
        id: qualityData.id,
        name: qualityData.machineNumber,
        reportId: qualityData.reportId,
      })
      machinePlan.taskId = dayTaskList.value.length - 1
    } else {
      machinePlan.taskId = flag
    }
  }
}

// 表单初始化 - 从老项目移植，适配Vue3语法
const formInitialization = () => {
  Object.assign(formData, {
    defQuantity: null,
    qualityItem: null,
    forRemark: null,
    remark: null,
    result: null,
    reason: null,
    batchNo: null,
  })

  // 重置图片列表
  imageList.value = []
}

// 根据质检任务ID获取质检信息 - 从老项目移植的getInfoByQualityTaskId方法，适配Vue3语法
const getInfoByQualityTaskId = async (qualityTaskId: number) => {
  try {
    uni.showLoading({
      title: '加载中',
    })

    const res = await taskApi.getInfoByQualityTaskId({
      qualityTaskId,
    })

    uni.hideLoading()

    if (!res) {
      isFormDisabled.value = true
      return false
    }

    // 处理返回的数据，与老项目逻辑保持一致
    const responseData = res as any
    isFormDisabled.value = false

    // 更新工单信息
    if (responseData.orderInfo) {
      Object.assign(workOrderInfo, responseData.orderInfo)

      // 判断当前任务是否已经结束，结束则禁用表单
      if (responseData.orderInfo.status) {
        isFormDisabled.value = true
      } else {
        isFormDisabled.value = false
      }

      // 显示工单信息卡片
      showWorkOrderInfo.value = true
    }

    // 处理质检信息
    if (responseData.qualityInfo) {
      Object.assign(qualityInfoData, responseData.qualityInfo)

      // 处理图片数据
      formData.imageValueList = []
      if (responseData.qualityInfo.forRemark) {
        formData.imageValueList = responseData.qualityInfo.forRemark
        imageList.value = responseData.qualityInfo.forRemark
      }

      // 处理质检项数据
      if (responseData.qualityInfo.qualityItem) {
        qualityInfoData.qualityItem = responseData.qualityInfo.qualityItem.map((item: any) => ({
          ...item,
          id: String(item.id), // 确保id是字符串类型
          checked: item.checked || false,
        }))

        // 处理已选择的质检项
        let selectedItems = ''
        responseData.qualityInfo.qualityItem.forEach((ele: any) => {
          if (ele.checked) {
            selectedItems += String(ele.id)
          }
        })
        formData.qualityItem = selectedItems || null
      }

      // 处理其他表单数据
      if (responseData.qualityInfo.isCompelete || responseData.qualityInfo.isComplete) {
        formData.defQuantity = responseData.qualityInfo.defQuantity || null
        formData.remark = responseData.qualityInfo.remark || null
        formData.reason = responseData.qualityInfo.reason || null
      } else {
        // 重置表单数据
        formInitialization()
      }
    }
  } catch (error) {
    uni.hideLoading()
    console.error('获取质检信息失败:', error)
    isFormDisabled.value = true

    uni.showToast({
      title: '获取质检信息失败',
      icon: 'none',
      duration: 2000,
    })
  }
}

// 提交表单 - 核心业务逻辑，保持与老项目的接口兼容性
const handleSubmit = async () => {
  // 将图片列表转换为逗号分隔的字符串，保持与老项目的兼容性
  // 这是为了适配后端期望的数据格式
  const imageValueList = imageList.value.map((item) => {
    // 如果图片上传组件返回的是完整路径，需要提取文件名
    if (typeof item === 'string') {
      return item
    } else if (item.url) {
      // 从URL中提取文件名，只保存文件名部分
      const urlParts = item.url.split('/')
      return urlParts[urlParts.length - 1]
    }
    return item
  })

  // 更新表单数据中的图片信息，确保数据格式正确
  formData.forRemark = imageValueList.join(',') // 逗号分隔的字符串格式
  formData.imageValueList = imageValueList // 数组格式，备用

  // 先进行业务逻辑校验，类似老项目的checkQualityData方法
  if (!(await checkQualityData())) {
    return
  }

  formRef.value
    .validate()
    .then(async () => {
      // 表单验证通过，提交数据
      console.log('提交的表单数据:', formData)

      uni.showLoading({
        title: '提交中...',
      })

      try {
        const res = await qualityCompletedApi.addQuality({
          workReportId: formData.workReportId,
          defQuantity: formData.defQuantity,
          forRemark: formData.forRemark,
          remark: formData.remark,
          result: formData.result,
          reason: formData.reason,
          batchNo: formData.batchNo,
        })

        uni.hideLoading()
        console.log(res, '新增成功')

        uni.showToast({
          title: '提交成功',
          duration: 2000,
        })

        // 跳转到列表页
        setTimeout(() => {
          uni.navigateTo({
            url: '/pages/quality/completed-inspection/index',
          })
        }, 300)
      } catch (error) {
        uni.hideLoading()
        console.error('提交失败:', error)
      }
    })
    .catch((error: any) => {
      console.log('表单验证失败:', error)
    })
}
// 获取质检任务详细信息 - 核心数据获取方法
const getQualityDetailData = async (workReportId: any) => {
  try {
    uni.showLoading({
      title: '加载中',
    })

    const res = await qualityCompletedApi.getQualityDetail({
      workReportId,
    })

    uni.hideLoading()
    console.log(res, 'res')

    // 根据接口返回的orderInfo字段进行拷贝
    const { orderInfo, qualityInfo } = (res as any) || {}

    // 处理质检信息数据
    if (qualityInfo) {
      Object.assign(qualityInfoData, qualityInfo)

      // 处理质检项数据，确保每个质检项都有正确的数据结构
      if (qualityInfo.qualityItem && Array.isArray(qualityInfo.qualityItem)) {
        qualityInfoData.qualityItem = qualityInfo.qualityItem.map((item: any) => ({
          ...item,
          id: String(item.id), // 确保id是字符串类型
          checked: item.checked || false,
          title: item.title || '',
          tool: item.tool || '',
          standrad: item.standrad || '', // 兼容不同的字段名
        }))
      }

      // 处理已有的图片数据
      if (qualityInfo.forRemark && Array.isArray(qualityInfo.forRemark)) {
        formData.imageValueList = qualityInfo.forRemark
      }

      // 处理表单数据
      if (qualityInfo.isCompelete) {
        formData.defQuantity = qualityInfo.defQuantity || null
        formData.remark = qualityInfo.remark || null
        formData.reason = qualityInfo.reason || null

        // 处理质检项选择状态，确保与checkbox组件的状态同步
        if (qualityInfo.qualityItem) {
          const selectedItems = qualityInfo.qualityItem
            .filter((item: any) => item.checked)
            .map((item: any) => String(item.id))
          formData.qualityItem = selectedItems.length > 0 ? selectedItems.join(',') : null

          // 同步更新质检项的选中状态，确保界面显示正确
          qualityInfoData.qualityItem.forEach((item: any) => {
            item.checked = selectedItems.includes(String(item.id))
          })
        }
      } else {
        // 重置表单数据
        formData.defQuantity = null
        formData.remark = null
        formData.reason = null
        formData.qualityItem = null
      }
    }

    console.log(qualityInfo, 'qualityInfo')

    // 处理工单信息
    if (orderInfo) {
      // 只拷贝图中存在的字段
      const {
        planQuantity = 0,
        shift = 0,
        scheduleDate = null,
        orderNum = '',
        productName = '',
        estimatedReceiptQuantity = 0,
        status = null,
      } = orderInfo

      // 更新表单数据（如有需要可补充）
      Object.assign(formData, {
        planQuantity,
        shift,
        scheduleDate,
        orderNum,
        productName,
        estimatedReceiptQuantity,
        status,
      })

      // 更新工单信息卡片显示内容
      Object.assign(workOrderInfo, orderInfo)
      console.log(workOrderInfo, 'workOrderInfo')

      // 根据状态判断表单是否禁用
      isFormDisabled.value = status === 1 // 如果状态为1（已完成），则禁用表单
    }

    // 显示工单信息卡片
    showWorkOrderInfo.value = true
  } catch (error) {
    uni.hideLoading()
    console.error('获取质检详情失败:', error)

    // 设置表单为禁用状态
    isFormDisabled.value = true

    uni.showToast({
      title: '获取数据失败',
      icon: 'none',
      duration: 2000,
    })
  }
}

// 报工id(任务id) - 已移至formData中，此变量保留以备后用
// const workReportId = ref(null)
onLoad((options) => {
  // 获取检验工具字典数据
  getDictData()
  console.log(toolList.value)
  // 初始化格式化后的日期范围
  formData.formattedDateRange = {
    startTime: formatDate(formData.dateRange[0]) + ' ' + '00:00:00',
    endTime: formatDate(formData.dateRange[1]) + ' ' + '23:59:59',
  }
  console.log(options, 'options')

  // 检查用户登录状态 - 使用新项目的用户store
  if (!userStore.userInfo || !userStore.userInfo.user) {
    uni.showToast({
      title: '请先登录',
      icon: 'none',
      duration: 2000,
    })
    uni.navigateTo({
      url: '/pages/login/login',
    })
    return
  }

  // 处理路由传参，与老项目逻辑保持一致
  if (options.qualityData) {
    // 路由传参的情况
    const qualityData = JSON.parse(options.qualityData)
    formData.workReportId = qualityData.reportId
    // 先获取任务列表，然后获取质检详情
    getDayTaskList(qualityData)
    getQualityDetailData(qualityData.id)
  } else if (options.reportId) {
    // 直接传reportId的情况 - 先获取任务列表，再获取质检详情
    formData.workReportId = options.reportId
    getDayTaskList()
  } else {
    // 没有传参的情况，获取今日任务
    getDayTaskList()
  }
})
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  padding-bottom: 120rpx;
  background-color: #f5f5f5;
}
/* 日期选择器 */
.date-selector {
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
}

.date-picker-wrapper {
  width: 100%;
}
/* 覆盖日历组件的默认样式 */
:deep(.wd-calendar-cell) {
  font-size: 32rpx;
}

:deep(.wd-calendar__value) {
  font-size: 30rpx !important;
  color: #333333;
}

:deep(.wd-calendar__label) {
  font-size: 30rpx;
}

.date-label {
  font-size: 28rpx;
  color: #666666;
  white-space: nowrap;
}

.date-value {
  flex: 1;
  margin-left: 10rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.date-icon {
  margin-left: 10rpx;
  color: #999999;
}
/* 生产机台选择 */
.production-line-section {
  padding: 30rpx;
  margin-bottom: 20rpx;
  background-color: #ffffff;
}

.production-line-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.production-line-label {
  font-size: 30rpx;
  color: #333333;
}

.production-line-picker {
  flex: 1;
  margin-left: 20rpx;
}

.picker-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
}

.picker-text {
  font-size: 30rpx;
  color: #333333;
}

.picker-arrow {
  display: flex;
  align-items: center;
}

.arrow-down {
  width: 0;
  height: 0;
  border-top: 10rpx solid #999999;
  border-right: 10rpx solid transparent;
  border-left: 10rpx solid transparent;
}
/* 信息卡片 */
.info-card {
  padding: 30rpx;
  margin-bottom: 20rpx;
  background-color: #ffffff;
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.header-line {
  width: 6rpx;
  height: 30rpx;
  margin-right: 15rpx;
  background-color: #4a90e2;
}

.card-title {
  flex: 1;
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.type-badge {
  padding: 8rpx 20rpx;
  font-size: 24rpx;
  color: #4a90e2;
  background-color: #e8f4fd;
  border-radius: 20rpx;
}
/* 信息列表 */
.info-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.info-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.info-label {
  font-size: 28rpx;
  color: #666666;
}

.info-value {
  font-size: 28rpx;
  color: #333333;
}
/* 表单项 */
.form-item {
  margin-bottom: 30rpx;
}

.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.form-label {
  display: block;
  margin-bottom: 15rpx;
  font-size: 28rpx;
  color: #333333;
}

.form-value {
  margin-left: auto;
  font-size: 28rpx;
  color: #333333;
}

.required {
  color: #ff4444;
}
.form-input {
  background-color: #f8f8f8;
  border-radius: 8rpx;
}

.form-textarea {
  min-height: 120rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
}
/* 扫描输入框 */
.input-with-scan {
  display: flex;
  align-items: center;
  padding-right: 15rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
}

.scan-input {
  flex: 1;
  background-color: transparent;
}

.scan-btn {
  padding: 10rpx;
}

.scan-icon {
  font-size: 40rpx;
  color: #4a90e2;
}

.upload-container {
  margin-top: 15rpx;
}

.submit-container {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 30rpx;
  background-color: #ffffff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.submit-btn {
  height: 88rpx;
  font-size: 32rpx;
  background-color: #4a90e2;
  border-radius: 8rpx;
}

.display-box {
  min-height: 60rpx;
  padding: 18rpx 24rpx;
  margin-bottom: 10rpx;
  font-size: 28rpx;
  color: #333;
  word-break: break-all;
  background: #f8f8f8;
  border-radius: 8rpx;
}
.remark-box {
  min-height: 100rpx;
  white-space: pre-line;
}
.display-text {
  font-size: 28rpx;
  line-height: 1.7;
  color: #333;
}

/* 质检项相关样式 */
.quality-items-container {
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
}

.quality-items-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.file-preview-btn {
  padding: 10rpx;
  background-color: #4a90e2;
  border-radius: 6rpx;
}

.file-icon {
  font-size: 32rpx;
  color: #ffffff;
}

.quality-items-scroll {
  max-height: 400rpx;
  overflow-y: auto;
}

.quality-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  padding: 15rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.checkbox-container {
  width: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15rpx;
}

.quality-checkbox {
  transform: scale(0.8);
}

.quality-item-content {
  flex: 1;
}

.quality-item-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 10rpx;
  line-height: 1.4;
}

.quality-item-detail {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.detail-row {
  display: flex;
  align-items: center;
}

.detail-label {
  font-size: 24rpx;
  color: #666666;
  width: 120rpx;
  flex-shrink: 0;
}

.detail-value {
  font-size: 24rpx;
  color: #999999;
  flex: 1;
  word-break: break-all;
}

/* 文件预览抽屉样式 */
.drawer-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.drawer-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.drawer-body {
  flex: 1;
  padding: 30rpx;
  overflow-y: auto;
}

.preview-section {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 28rpx;
  color: #4a90e2;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #f8f8f8;
  display: block;
  width: 100%;
}

.file-item {
  margin-bottom: 20rpx;
}

.file-name {
  font-size: 28rpx;
  color: #4a90e2;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #f8f8f8;
  display: block;
  width: 100%;
  word-break: break-all;
}
</style>
