<route lang="json5">
{
  needLogin: true,
  style: {
    navigationBarTitleText: '完工检验',
    enablePullDownRefresh: true,
  },
}
</route>

<template>
  <view class="container">
    <!-- 顶部搜索区域 -->
    <view class="search-bar">
      <wd-input
        v-model="searchKeyword"
        placeholder="请输入搜索关键词"
        clearable
        prefix-icon="search"
        @input="handleSearch"
      />
    </view>

    <!-- 使用封装的质量检验列表组件 -->
    <component
      :is="QualityListComponent"
      :list="completedList"
      :loading="loading"
      empty-text="暂无检验记录"
      title-field="productName"
      :display-fields="displayFields"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import QualityList from '@/components/quality/QualityList.vue'
import { onShow } from '@dcloudio/uni-app'
import { getQualityList } from '@/service/quality/completed'
import { useUserStore } from '@/store'
// 重命名导入，避免TypeScript错误
const QualityListComponent = QualityList

// 搜索关键词
const searchKeyword = ref('')
// 添加loading状态
const loading = ref(false)

// 完工检验列表 - 根据后台返回的数据结构进行初始化
const completedList = ref([])
// 获取数据 - 根据后台返回的数据结构进行处理
function fetchData(searchParams = {}) {
  loading.value = true

  // 合并搜索参数
  const params = {
    page: 0,
    size: 10,
    ...searchParams,
  }

  getQualityList(params)
    .then((res) => {
      console.log(res, '完工检验列表')
      if (res.code === 200 && res.data?.content) {
        // 将后台数据映射为前端需要的格式
        const mappedData = res.data.content.map((item: any) => ({
          id: item.id,
          productName: item.materialName, // 后台字段materialName对应前端productName
          machineOrderNum: item.machineOrderNum, // 机加工单号
          planQuantity: item.planQuantity, // 计划数量
          receiptQuantity: item.receiptQuantity, // 完工数量
          defQuantity: item.defQuantity, // 不良品数量
          procedureName: item.procedureName, // 工序名称
          nickName: item.nickName, // 操作人员
          qualityTime: item.qualityTime, // 质检时间
          createTime: item.createTime, // 创建时间
          status: item.status, // 状态：0-待检验，1-合格，2-不合格
          statusText: item.status === 0 ? '待检验' : item.status === 1 ? '合格' : '不合格',
          unit: '件', // 默认单位
        }))

        // 直接设置显示数据，不再需要保存原始数据用于前端过滤
        completedList.value = mappedData
      }
    })
    .catch((err) => {
      console.log(err)
      uni.showToast({
        title: '获取数据失败',
        icon: 'none',
      })
    })
    .finally(() => {
      loading.value = false
    })
}

// 显示字段配置 - 根据后台数据结构调整
const displayFields = [
  { label: '机加工单号', field: 'machineOrderNum' },
  { label: '计划数量', field: 'planQuantity', unit: 'unit' },
  { label: '完工数量', field: 'receiptQuantity', unit: 'unit' },
  { label: '工序名称', field: 'procedureName' },
  { label: '操作人员', field: 'nickName' },
  { label: '质检时间', field: 'qualityTime' },
]

// 搜索防抖定时器
let searchTimer: NodeJS.Timeout | null = null

// // 查看详情
// const viewDetail = (item) => {
//   console.log('查看详情:', item)
//   // 跳转到详情页面
//   uni.navigateTo({
//     url: `/pages/quality/completed-inspection/detail?id=${item.id}`,
//     fail: (err) => {
//       console.error('导航失败:', err)
//       uni.showToast({
//         title: '页面开发中...',
//         icon: 'none',
//       })
//     },
//   })
// }

// 前往表单页面
const goToForm = () => {
  uni.navigateTo({
    url: '/pages/quality/completed-inspection/form',
  })
}

// 处理搜索 - 根据machineOrderNum字段发请求到后端搜索
const handleSearch = () => {
  // 清除之前的定时器
  if (searchTimer) {
    clearTimeout(searchTimer)
  }

  // 防抖处理，避免频繁请求
  searchTimer = setTimeout(() => {
    if (!searchKeyword.value.trim()) {
      // 搜索词为空，获取全部数据
      fetchData()
      return
    }

    // 根据machineOrderNum字段发请求到后端搜索
    const searchParams = {
      machineOrderNum: searchKeyword.value.trim(), // 使用机加工单号进行搜索
    }

    console.log('搜索参数:', searchParams)
    fetchData(searchParams)
  }, 500) // 500ms防抖延迟
}

onMounted(() => {
  // 页面加载时获取数据
  fetchData()
})

// 页面显示时刷新数据
onShow(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.container {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  min-height: 100vh;
  padding-bottom: 120rpx; /* 为底部按钮留出空间 */
  overflow-x: hidden;
  background-color: #f5f5f5;

  & > view {
    box-sizing: border-box;
    width: 100%;
    padding: 0;
    margin: 0;
    list-style-type: none;
  }
}

.search-bar {
  position: sticky;
  top: 0;
  z-index: 10;
  box-sizing: border-box;
  width: 100%;
  padding: 20rpx 30rpx;
  margin: 0;
  background-color: #ffffff;
}

.bottom-button {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 10;
  width: 100%;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
</style>
