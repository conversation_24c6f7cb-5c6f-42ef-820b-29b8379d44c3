# 变更日志 - 2025年06月10日

## [15:04:03] [refactor]: 重构首页功能路由跳转逻辑

**作者**: 开发团队  
**文件**: src/pages/index/index.vue

### 变更描述

重构首页功能模块的路由跳转逻辑，由基于角色ID跳转改为基于角色名称跳转，并优化了多角色用户的功能访问体验。

### 变更详情

**原代码**:

```javascript
// 根据角色显示不同的功能列表
const functionList = computed(() => {
  // 检查用户是否具有质量角色（ID为18的质量部角色）
  const isQualityRole = userStore.userInfo?.user?.roles?.[0]?.id === 18
  // 检查用户是否具有调机角色（ID为17的调机角色）
  const isMachineSetupRole = userStore.userInfo?.user?.roles?.[0]?.id === 17

  if (isQualityRole) {
    return qualityFunctionList
  } else if (isMachineSetupRole) {
    return machineSetupFunctionList
  } else {
    return normalFunctionList
  }
})

const handleModuleClick = (item: any) => {
  console.log('点击功能模块:', item.name)

  // 检查是否有定义URL
  if (item.url) {
    uni.navigateTo({
      url: item.url,
      fail: (err) => {
        console.error('导航失败:', err)
        // 如果导航失败，可能是页面不存在，提示用户
        uni.showToast({
          title: '功能开发中...',
          icon: 'none',
        })
      },
    })
  } else {
    uni.showToast({
      title: '功能开发中...',
      icon: 'none',
    })
  }
}
```

**新代码**:

```javascript
// 定义质量角色专属功能列表
const qualityFunctionList = [
  {
    name: '来料检',
    icon: 'search',
    bgColor: '#fa3534',
    url: '/pages/quality/incoming-inspection/index',
  },
  // ...其他质检功能
]

// 定义调机功能列表
const machineSetupFunctionList = [
  {
    name: 'CNC调机',
    icon: 'tools',
    bgColor: '#4c6ef5',
    url: '/pages/machine/cnc/index',
  },
  // ...其他调机功能
]

// 根据角色显示不同的功能列表
const functionList = computed(() => {
  // 获取所有角色名称
  const roles = userStore.userInfo?.user?.roles
  const roleNames = Array.isArray(roles) ? roles.map((role) => role.name) : []

  // 所有用户都显示通用功能列表，特殊处理逻辑在handleModuleClick中实现
  return normalFunctionList
})

// popup弹出框配置
const popupConfig = ref({
  show: false,
  title: '',
  options: [] as Array<{
    name: string,
    icon: string,
    bgColor: string,
    url: string
  }>
})

const handleModuleClick = (item: any) => {
  // 获取角色信息
  const roles = userStore.userInfo?.user?.roles
  const roleNames = Array.isArray(roles) ? roles.map((role) => role.name) : []
  const isAdmin = roleNames.includes('超级管理员') || roleNames.includes('admin') || userStore.userInfo?.user?.username === 'admin'

  // 针对"调机"模块的特殊处理
  if (item.name === '调机') {
    const hasCncRole = roleNames.includes('CNC调机师')
    const hasCastingRole = roleNames.includes('压铸调机师')

    // 如果用户同时拥有两种调机角色或是超级管理员，弹出选择框
    if (hasCncRole && hasCastingRole || isAdmin) {
      popupConfig.value = {
        show: true,
        title: '请选择调机类型',
        options: machineSetupFunctionList
      }
      return // 阻止后续代码执行
    }
    // 如果只拥有CNC调机角色，直接跳转
    else if (hasCncRole) {
      uni.navigateTo({ url: '/pages/machine/cnc/index' })
      return
    }
    // ...其他处理逻辑
  }

  // 针对"质检"模块的特殊处理
  if (item.name === '质检') {
    const hasQualityRole = roleNames.includes('质量')

    // 如果用户拥有质量角色或是超级管理员，弹出选择框
    if (hasQualityRole || isAdmin) {
      popupConfig.value = {
        show: true,
        title: '请选择质检类型',
        options: qualityFunctionList
      }
      return // 阻止后续代码执行
    }
  }

  // 常规跳转处理
  // ...
}

// 处理选项点击
const handleOptionClick = (url: string) => {
  popupConfig.value.show = false
  uni.navigateTo({ url })
}
```

**动态弹出框模板**:

```html
<!-- 动态选项弹出框 -->
<wd-popup v-model="popupConfig.show" position="top" custom-style="padding: 30rpx;">
  <view class="popup-title">{{ popupConfig.title }}</view>
  <view class="popup-options">
    <view
      class="popup-option"
      v-for="(option, index) in popupConfig.options"
      :key="index"
      @click="handleOptionClick(option.url)"
    >
      <view class="popup-icon-wrapper" :style="{ backgroundColor: option.bgColor }">
        <wd-icon :name="option.icon" size="28" color="#FFFFFF" />
      </view>
      <text class="popup-option-text">{{ option.name }}</text>
    </view>
  </view>
</wd-popup>
```
