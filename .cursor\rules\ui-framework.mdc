---
description: 
globs: 
alwaysApply: true
---
# UI框架指南

## 主要UI框架

项目主要使用**wot-design-uni**作为UI组件库，这是一个基于uni-app的移动端组件库，由京东设计开发。

```typescript
// 使用wot-design-uni组件示例
<wd-button type="primary">按钮</wd-button>
<wd-cell title="标题" value="内容"></wd-cell>
```

## 样式系统

样式系统由多个部分组成：

1. **UnoCSS** - 原子化CSS框架，通过类名直接应用样式
   ```html
   <view class="flex items-center justify-between p-4"></view>
   ```

2. **SCSS** - 全局样式定义在`src/style`目录下

3. **uni.scss** - uni-app内置样式变量

## 图标系统

项目使用**Iconify**作为图标库，配置了Carbon图标集：

```
@iconify-json/carbon
```

## 适配策略

项目采用uni-app跨平台开发，支持：

- H5
- 微信小程序
- App
- 其他小程序平台

通过条件编译实现不同平台的适配：

```vue
<!-- #ifdef H5 -->
<view>H5 环境特有内容</view>
<!-- #endif -->

<!-- #ifdef MP-WEIXIN -->
<view>微信小程序特有内容</view>
<!-- #endif -->
```

## 通用页面模式

### 列表页面

列表页面通常包含以下几个部分：

1. **顶部搜索栏** - 使用`wd-input`组件，添加搜索图标
2. **内容列表** - 使用`z-paging`组件实现下拉刷新和上拉加载
3. **列表项** - 使用卡片式布局，包含标题、状态标签、详情信息和时间等
4. **底部按钮** - 固定在底部，使用`wd-button`组件，通常为全宽按钮

```vue
<!-- 列表页面示例 -->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <wd-input placeholder="搜索" prefix-icon="search" />
  </view>

  <!-- 列表内容 -->
  <z-paging ref="paging" @query="queryList">
    <view class="list-item" v-for="item in list">
      <!-- 列表项内容 -->
    </view>
  </z-paging>

  <!-- 底部按钮 -->
  <view class="bottom-button">
    <wd-button type="primary" block>新增</wd-button>
  </view>
</view>
```

### 表单页面

表单页面通常包含以下几个部分：

1. **表单项组** - 白色背景，每个表单项包含标签和输入控件
2. **提交按钮** - 固定在底部，使用`wd-button`组件，通常为全宽按钮
3. **图片上传区** - 使用九宫格布局，带有添加按钮和删除功能

```vue
<!-- 表单页面示例 -->
<view class="container">
  <form @submit="handleSubmit">
    <!-- 表单内容 -->
    <view class="form-content">
      <view class="form-item" v-for="item in formItems">
        <view class="form-label">{{ item.label }}</view>
        <view class="form-value">
          <!-- 输入控件 -->
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-button">
      <wd-button type="primary" block form-type="submit">提交</wd-button>
    </view>
  </form>
</view>
```

## 质量检验模块UI规范

质量检验模块遵循以下特定UI规范：

### 色彩规范

- **主色调**: 品质检验使用蓝色系列 (#2979ff) 作为模块主色
- **状态颜色**:
  - 合格: 绿色 (#19be6b)
  - 不合格: 红色 (#fa3534)
  - 待检验: 橙色 (#ff9900)

### 来料检验列表页

- 采用卡片式布局，突出显示材料名称和检验状态
- 使用标签显示检验状态，采用对应的状态颜色
- 列表项中显示关键信息：批次号、数量、不良品数量、检验人员
- 底部固定"新增来料检验"按钮

### 来料检验表单

- 表单项布局为上下结构：标签在上，输入控件在下
- 必填项标签带有红色星号标识
- 系统自动填充的字段使用灰色背景区分
- 图片上传区支持多图上传，采用九宫格布局
- 表单底部固定提交按钮

### 其他质量检验页面

委外加工检验和完工检验页面应保持与来料检验页面相同的UI风格和交互模式，确保整个质量检验模块的用户体验一致性。

