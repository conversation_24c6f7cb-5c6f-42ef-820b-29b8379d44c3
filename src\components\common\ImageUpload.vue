<template>
  <wd-upload
    :action="action"
    :file-list="fileList"
    :before-upload="beforeUpload"
    :disabled="disabled"
    :limit="maxCount"
    @success="handleSuccess"
    @remove="handleRemove"
  >
    <wd-button>
      <wd-icon name="camera" />
      选择图片
    </wd-button>
  </wd-upload>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'
import { useToast } from 'wot-design-uni'

// --- 关键改动 1: 明确声明所有支持的 Prop ---
// 这样可以提供更好的类型检查和代码提示
const props = defineProps({
  // 上传的服务器地址
  action: { type: String, required: true },
  // 文件列表 (用于 v-model)
  fileList: { type: Array, default: () => [] },
  // 上传前的钩子
  beforeUpload: { type: Function, default: null },
  // 其他常用的 wd-upload 属性，按需添加
  disabled: { type: Boolean, default: false },
  maxCount: { type: Number, default: 9 }, // 例如最大数量
})

// --- 关键改动 2: 明确声明所有会向外触发的事件 ---
const emit = defineEmits([
  'update:fileList', // 用于 v-model
  'upload-success', // 用于父组件监听 @upload-success
  'error', // 用于父组件监听 @error
  'remove', // 用于父组件监听 @remove
])

const toast = useToast()

// --- 关键改动 3: 在处理函数中，手动 emit 事件给父组件 ---

// 上传成功处理
const handleSuccess = ({ file, fileList: newFileList }) => {
  toast.show('上传成功')
  // 1. 更新 v-model
  emit('update:fileList', newFileList)
  // 2. 将 success 事件和数据"透传"给父组件
  emit('upload-success', { file, fileList: newFileList })
}

// 上传失败处理
const handleError = ({ file, fileList: newFileList }) => {
  toast.error('上传失败')
  // 1. 更新 v-model
  emit('update:fileList', newFileList)
  // 2. 将 error 事件和数据"透传"给父组件
  emit('error', { file, fileList: newFileList })
}

// 文件移除处理
const handleRemove = ({ file, fileList: newFileList }) => {
  // 1. 更新 v-model
  emit('update:fileList', newFileList)
  // 2. 将 remove 事件和数据"透传"给父组件
  emit('remove', { file, fileList: newFileList })
}
</script>
