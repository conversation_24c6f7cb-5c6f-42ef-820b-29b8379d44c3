<route lang="json5">
{
  layout: 'default',
  needLogin: true,
  style: {
    navigationBarTitleText: '排班',
  },
}
</route>

<template>
  <view class="page-container">
    <!-- 筛选条件卡片 -->
    <wd-card class="filter-card">
      <template #title>
        <view class="card-title">
          <wd-icon name="filter" size="16" />
          <text>筛选条件</text>
        </view>
      </template>

      <!-- 筛选条件内容 -->
      <view class="filter-content">
        <!-- 日期选择器 -->
        <view class="filter-item">
          <text class="filter-label">选择日期</text>
          <view class="filter-value">
            <wd-calendar
              v-model="dateRange"
              type="daterange"
              label=""
              allow-same-day
              @confirm="handleDateChange"
            />
          </view>
        </view>

        <!-- 加工单选择 -->
        <view class="filter-item">
          <text class="filter-label">加工单</text>
          <view class="filter-value">
            <wd-picker
              v-model="workOrder"
              :columns="workOrderOptions"
              @confirm="handleWorkOrderChange"
            >
              <view class="picker-display">
                <text class="picker-text">{{ workOrder }}</text>
                <wd-icon name="arrow-right" size="12" color="#c0c4cc" />
              </view>
            </wd-picker>
          </view>
        </view>

        <!-- 班次信息 -->
        <view class="filter-item">
          <text class="filter-label">班次</text>
          <view class="filter-value">
            <text class="value-text">{{ shift }}</text>
          </view>
        </view>

        <!-- 工作时间 -->
        <view class="filter-item">
          <text class="filter-label">工作时间</text>
          <view class="filter-value">
            <text class="value-text">{{ date }}</text>
          </view>
        </view>
      </view>
    </wd-card>

    <!-- 设备状态网格 -->
    <wd-card v-if="devices.length > 0" class="device-card">
      <template #title>
        <view class="card-title">
          <wd-icon name="device" size="16" />
          <text>设备状态</text>
          <wd-tag size="small">{{ devices.length }}台</wd-tag>
        </view>
      </template>

      <view class="device-grid">
        <view
          v-for="item in devices"
          :key="item.id"
          class="device-item"
          :class="getDeviceStatusClass(item)"
          @click="handleDeviceClick(item)"
        >
          <!-- 设备编号 -->
          <view class="device-header">
            <text class="device-code">{{ item.machineNum }}</text>
            <wd-icon :name="getDeviceIcon(item)" :color="getDeviceIconColor(item)" size="14" />
          </view>

          <!-- 设备状态信息 -->
          <view class="device-content">
            <text class="device-name">{{ getDeviceDisplayName(item) }}</text>
            <wd-tag :type="getDeviceTagType(item)" size="small" class="device-status-tag">
              {{ getDeviceStatusText(item) }}
            </wd-tag>
          </view>
        </view>
      </view>
    </wd-card>

    <!-- 空状态 -->
    <view v-else class="empty-state">
      <wd-img
        src="/static/images/empty/default-empty.png"
        width="200rpx"
        height="200rpx"
        mode="aspectFit"
      />
      <text class="empty-text">暂无设备数据</text>
    </view>

    <!-- 底部操作按钮 -->
    <view class="bottom-action">
      <wd-button block :disabled="taskStatus" type="primary" size="large" @click="handleSubmit">
        <wd-icon name="check" size="16" />
        任务结束
      </wd-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { timestampsToDateStrings, formatDateString } from '@/utils/time'
import { getMachineOrderList, getScheduleList, complete } from '@/service/arrangement/tasks'
import { IScheduleListRes } from '@/service/arrangement/types'

const currentDate = new Date()
const dateRange = ref<number[]>([currentDate.getTime(), currentDate.getTime()])
const workOrder = ref('暂无数据')
const shift = ref('')
const date = ref('')
const workOrderOptions = ref(['暂无数据'])
const workOrderData = ref([])
const scheduleList = ref<IScheduleListRes>()
const taskStatus = ref<boolean>(true)

const devices = ref([])

// 获取设备状态样式类
const getDeviceStatusClass = (item: any) => {
  if (item.deviceStatus === 1) {
    return 'device-disabled' // 停用设备
  }
  if (item.taskStatus === 2) {
    return 'device-error' // 异常任务
  }
  if (item.scheduleExist) {
    return 'device-active' // 正常排班
  }
  return 'device-idle' // 未排班
}

// 获取设备图标
const getDeviceIcon = (item: any) => {
  if (item.deviceStatus === 1) {
    return 'close-circle' // 停用
  }
  if (item.taskStatus === 2) {
    return 'warning' // 异常
  }
  if (item.scheduleExist) {
    return 'check-circle' // 正常
  }
  return 'time-circle' // 待排班
}

// 获取设备图标颜色
const getDeviceIconColor = (item: any) => {
  if (item.deviceStatus === 1) {
    return '#fa3534' // 红色 - 停用
  }
  if (item.taskStatus === 2) {
    return '#ff9900' // 橙色 - 异常
  }
  if (item.scheduleExist) {
    return '#19be6b' // 绿色 - 正常
  }
  return '#909399' // 灰色 - 待排班
}

// 获取设备显示名称
const getDeviceDisplayName = (item: any) => {
  if (item.deviceStatus === 1) {
    return '设备停用'
  }
  if (item.taskStatus === 2) {
    return '异常任务'
  }
  if (item.scheduleExist && item.nickName) {
    return item.nickName
  }
  return '未排班'
}

// 获取设备标签类型
const getDeviceTagType = (item: any) => {
  if (item.deviceStatus === 1) {
    return 'danger' // 停用
  }
  if (item.taskStatus === 2) {
    return 'warning' // 异常
  }
  if (item.scheduleExist) {
    return 'success' // 正常
  }
  return 'primary' // 待排班
}

// 获取设备状态文本
const getDeviceStatusText = (item: any) => {
  if (item.deviceStatus === 1) {
    return '停用'
  }
  if (item.taskStatus === 2) {
    return '异常'
  }
  if (item.scheduleExist) {
    return '正常'
  }
  return '待排班'
}

const fetchMachineOrders = async () => {
  const dates = timestampsToDateStrings(dateRange.value)
  console.log('dateRange:', dates)
  const res = await getMachineOrderList({
    startTime: dates[0] + ' 00:00:00',
    endTime: dates[1] + ' 23:59:59',
    status: 1,
  })
  workOrderData.value = res.data
  if (res.data.length > 0) {
    workOrderOptions.value = res.data.map((item: any) => item.machineOrderNum)
    workOrder.value = res.data[0].machineOrderNum || ''
    shift.value = res.data[0].shift || ''
    date.value = res.data[0].scheduleDate || ''
    await fetchScheduleList(res.data[0].id)
  }
}
const fetchScheduleList = async (machineOrderId: number) => {
  const res = await getScheduleList(machineOrderId)
  scheduleList.value = res.data
  taskStatus.value = res.data.status === 1
  devices.value = res.data.scheduleList
}

const shiftMap = {
  0: '白班',
  1: '晚班',
}

onLoad(async (option) => {
  console.log('option', option)
  if (option.scheduleData) {
    const scheduleData = JSON.parse(option.scheduleData)
    const scheduleDate = formatDateString(scheduleData.scheduleDate)
    dateRange.value = [new Date(scheduleDate).getTime(), new Date(scheduleDate).getTime()]
    const dates = timestampsToDateStrings(dateRange.value)
    const res = await getMachineOrderList({
      startTime: dates[0] + ' 00:00:00',
      endTime: dates[1] + ' 23:59:59',
      status: 1,
    })
    workOrderData.value = res.data
    workOrderOptions.value = res.data.map((item: any) => item.machineOrderNum)
    workOrder.value = scheduleData.machineOrderNum
    shift.value = shiftMap[scheduleData.shift]
    date.value = scheduleDate
    await fetchScheduleList(scheduleData.id)
  } else {
    fetchMachineOrders()
  }
})

const handleDateChange = () => {
  fetchMachineOrders()
}

const handleDeviceClick = (device: any) => {
  console.log('设备点击:', device)
  device.machineOrderNum = workOrder.value
  device.scheduleDate = date.value
  device.shift = shift.value
  if (device.scheduleExist && device.deviceStatus !== 1) {
    uni.setStorageSync('schMachine', device)
    uni.navigateTo({
      url: '/pages/arrangement/arrange',
    })
  } else if (!device.scheduleExist) {
    uni.setStorageSync('schMachine', device)
    uni.navigateTo({
      url: '/pages/arrangement/arrange',
    })
  }
}

const handleWorkOrderChange = (value: string) => {
  workOrder.value = value
  const selectedOrder = workOrderData.value.find((item: any) => item.machineOrderNum === value)
  if (selectedOrder) {
    shift.value = selectedOrder.shift
    date.value = selectedOrder.scheduleDate
  }
}

const handleSubmit = () => {
  console.log('任务结束')
  uni.showModal({
    title: '提示',
    content: '确定完成此排班任务？',
    cancelText: '取消',
    confirmText: '确定',
    success: function (res) {
      if (res.confirm) {
        // 点击确认时，执行
        const selectedOrder = workOrderData.value.find(
          (item: any) => item.machineOrderNum === workOrder.value,
        )
        complete(selectedOrder.id).then((res) => {
          if (res.data) {
            uni.showToast({
              title: '任务完成',
              icon: 'success',
              duration: 2000,
            })
          }
          setTimeout(function () {
            uni.switchTab({
              url: '/pages/index/index',
            })
          }, 2000)
        })
      }
    },
  })
}
</script>

<style lang="scss" scoped>
@import '@/style/design-system.scss';

.page-container {
  min-height: 100vh;
  padding: $spacing-md;
  padding-bottom: 120rpx; // 为底部按钮留出空间
  background-color: $bg-page;
}

/* ==================== 卡片样式 ==================== */
.filter-card,
.device-card {
  margin-bottom: $spacing-md;

  :deep(.wd-card) {
    border-radius: $border-radius-lg;
    box-shadow: $shadow-light;
  }
}

.card-title {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  font-size: $font-size-lg;
  font-weight: $font-weight-medium;
  color: $text-primary;
}

/* ==================== 筛选条件样式 ==================== */
.filter-content {
  padding: $spacing-sm 0;
}

.filter-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-md 0;
  border-bottom: 1rpx solid $border-light;

  &:last-child {
    border-bottom: none;
  }
}

.filter-label {
  font-size: $font-size-base;
  color: $text-primary;
  font-weight: $font-weight-medium;
  min-width: 120rpx; // 固定标签宽度，确保对齐
  flex-shrink: 0; // 防止标签收缩
}

.filter-value {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  min-width: 0; // 允许内容收缩
}

/* ==================== 日期显示样式 ==================== */
.date-display {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  cursor: pointer;
}

.date-text {
  font-size: $font-size-base;
  color: $text-secondary;
  text-align: right;
}

.value-text {
  font-size: $font-size-base;
  color: $text-secondary;
  text-align: right;
}

/* ==================== 选择器显示样式 ==================== */
.picker-display {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  cursor: pointer;
}

.picker-text {
  font-size: $font-size-base;
  color: $text-secondary;
  text-align: right;
  white-space: nowrap; // 防止换行
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300rpx; // 限制最大宽度
}

/* ==================== 设备网格样式 ==================== */
.device-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160rpx, 1fr));
  gap: $spacing-md;
  padding: $spacing-sm 0;
}

.device-item {
  position: relative;
  padding: $spacing-md;
  background-color: $bg-card;
  border-radius: $border-radius-lg;
  border: 2rpx solid transparent;
  transition: all $duration-base $ease-out;
  cursor: pointer;

  &:active {
    transform: scale(0.98);
  }

  // 设备状态样式
  &.device-active {
    border-color: $success-color;
    background: linear-gradient(
      135deg,
      rgba($success-color, 0.1) 0%,
      rgba($success-color, 0.05) 100%
    );
  }

  &.device-idle {
    border-color: $info-color;
    background: linear-gradient(135deg, rgba($info-color, 0.1) 0%, rgba($info-color, 0.05) 100%);
  }

  &.device-error {
    border-color: $warning-color;
    background: linear-gradient(
      135deg,
      rgba($warning-color, 0.1) 0%,
      rgba($warning-color, 0.05) 100%
    );
  }

  &.device-disabled {
    border-color: $error-color;
    background: linear-gradient(135deg, rgba($error-color, 0.1) 0%, rgba($error-color, 0.05) 100%);
    opacity: 0.7;
  }
}

.device-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: $spacing-sm;
}

.device-code {
  font-size: $font-size-lg;
  font-weight: $font-weight-bold;
  color: $text-primary;
}

.device-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $spacing-xs;
}

.device-name {
  font-size: $font-size-sm;
  color: $text-secondary;
  text-align: center;
  line-height: $line-height-tight;
}

.device-status-tag {
  :deep(.wd-tag) {
    font-size: $font-size-xs;
    padding: 4rpx 8rpx;
    border-radius: $border-radius-sm;
  }
}

/* ==================== 空状态样式 ==================== */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-xxl;
  margin-top: $spacing-xl;
}

.empty-text {
  margin-top: $spacing-md;
  font-size: $font-size-base;
  color: $text-tertiary;
}

/* ==================== 底部操作按钮 ==================== */
.bottom-action {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: $spacing-md $spacing-lg;
  background-color: $bg-card;
  border-top: 1rpx solid $border-light;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);

  :deep(.wd-button) {
    border-radius: $border-radius-lg;
    font-weight: $font-weight-medium;

    .wd-button__text {
      display: flex;
      align-items: center;
      gap: $spacing-xs;
    }
  }
}

/* ==================== 响应式适配 ==================== */
@media (max-width: 750rpx) {
  .device-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 600rpx) {
  .device-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
