import { http } from '@/utils/http'
import { IWorkReportBeforeRes, IWorkOrderRes, IWorkReportForm } from '@/service/report/types'

export const getOrderInfoBefore = (machineNumber: string) => {
  return http.get<IWorkReportBeforeRes[]>('/api/v1/terminal/work-report/reportGetOrderInfoBefore', {
    machineNumber,
  })
}

export const getOrderInfo = (machineTaskId: number) => {
  return http.get<IWorkOrderRes>('/api/v1/terminal/work-report/reportGetOrderInfo', {
    machineTaskId,
  })
}

export const addWorkReport = (data: IWorkReportForm) => {
  return http.post<boolean>('/api/v1/terminal/work-report/addWorkReport', data)
}

export const shutdownReasonList = () => {
  return http.get<string[]>('/api/v1/terminal/work-report/shutdownReasonList', {})
}

export const getNextTraceCode = () => {
  return http.get<string>('/api/v1/terminal/work-report/new-trace-code', {})
}
