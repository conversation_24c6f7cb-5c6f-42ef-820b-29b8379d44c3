<route lang="json5">
{
  layout: 'tabbar',
  needLogin: true,
  style: {
    navigationBarTitleText: '快速报工',
  },
}
</route>

<template>
  <view class="code">
    <view class="content"></view>
  </view>
</template>

<script lang="ts" setup>
import { useToast } from 'wot-design-uni'
import { QrcodeDecoder } from 'qrcode-decoder'
import { getObjectURL } from '@/utils/config'

const toast = useToast()

onShow(() => {
  scanCode()
})

function scanCode() {
  console.log('scanCode')
  // #ifdef H5
  scanCodeH5()
  // #endif

  // #ifdef APP-PLUS
  scanCodeAPP()
  // #endif
}

function scanCodeAPP() {
  uni.scanCode({
    success: (res) => {
      jumpWorkReport(res.result)
    },
  })
}

function scanCodeH5() {
  const qrcode = new QrcodeDecoder()
  uni.chooseImage({
    count: 1,
    success: (imgRes) => {
      qrcode
        .decodeFromImage(getObjectURL(imgRes.tempFiles[0]))
        .then((res) => {
          toast.success({
            msg: '扫码成功',
          })
          console.log(res)
          jumpWorkReport(res.data)
        })
        .catch((err) => {
          toast.error({
            msg: '扫码失败',
          })
          console.log(err)
        })
    },
  })
}

function jumpWorkReport(machineNumber: string) {
  uni.navigateTo({
    url: '/pages/report/work?machineNumber=' + machineNumber,
  })
}

function handleBack() {
  uni.switchTab({
    url: '/pages/index/index',
  })
}
</script>

<style lang="scss" scoped>
.code {
  width: 100%;
  height: 100%;
}

.content {
  padding: 16px;
}
</style>
