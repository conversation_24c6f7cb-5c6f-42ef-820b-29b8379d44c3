export interface IAdjustmentRes {
  content: IAdjustmentItem[]
  totalElements: number
}

export interface IMachineLatestTask {
  machineNumber: string
  scheduleDate: string
  materialName: string
  procedureName: string
  deviceType: number
  planQuantity: number
  completedQuantity: number
  remark: string
  partsName: string
  id: number
  order_complete_status: boolean
  plan_complete_status: number
}

export interface IAdjustmentBind {
  deviceId: number
  partsId: number
  creatorId: number
}

export interface IAdjustmentItem {
  partsName: string
  machineNumber: string
  creatorName: string
  createTime: string
}
