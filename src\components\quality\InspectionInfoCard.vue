<template>
  <view class="card-section">
    <view class="section-title">{{ title }}</view>
    <view class="info-grid">
      <view class="info-item" v-for="(item, index) in infoItems" :key="index">
        <text class="info-label">{{ item.label }}</text>
        <text class="info-value">{{ item.value }} {{ item.unit || '' }}</text>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
interface InfoItem {
  label: string
  value: string | number
  unit?: string
}

export default {
  name: 'InspectionInfoCard',
  props: {
    // 卡片标题
    title: {
      type: String,
      default: '基本信息',
    },
    // 要显示的信息项数组
    infoItems: {
      type: Array<InfoItem>,
      default: () => [],
    },
  },
  setup() {
    return {}
  },
}
</script>

<style lang="scss">
.card-section {
  padding-bottom: 20rpx;
  margin-bottom: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.card-section:last-child {
  padding-bottom: 0;
  margin-bottom: 0;
  border-bottom: none;
}

.section-title {
  margin-bottom: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.info-grid {
  display: flex;
  flex-wrap: wrap;
}

.info-item {
  width: 50%;
  margin-bottom: 20rpx;
}

.info-label {
  display: block;
  margin-bottom: 8rpx;
  font-size: 28rpx;
  color: #999999;
}

.info-value {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
}
</style>
