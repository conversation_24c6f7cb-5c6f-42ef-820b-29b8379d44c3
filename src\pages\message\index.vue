<route lang="json5">
{
  type: 'page',
  layout: 'default',
  needLogin: true,
  style: {
    navigationBarTitleText: '消息通知',
  },
}
</route>
<template>
  <view class="message-page">
    <view background="#F3F3F3">
      <view v-for="item in messageList" :key="item.id" class="message-box">
        <view class="time-sty">{{ item.createTime }}</view>
        <view :class="item.readStatus === 0 ? 'taskTopBorder' : 'taskTopBorder2'"></view>
        <view class="task">
          <view class="task-title">
            <view class="msgBox">
              <wd-icon name="notification" size="16" />
            </view>
            <view class="task-cont">
              {{ item.content }}
            </view>
          </view>
          <view class="task-cont-box">
            <view class="task-left">
              <view class="task-one-box">
                <view class="cont-title">加工单：</view>
                <view class="cont-value">{{ item.machineOrderNum }}</view>
              </view>
              <view class="task-one-box">
                <view class="cont-title">工单时间：</view>
                <view class="cont-value">{{ formatDateString(item.scheduleDate) }}</view>
              </view>
              <view class="task-one-box" v-if="item.machineNumber">
                <view class="cont-title">机台号：</view>
                <view class="cont-value">{{ item.machineNumber }}</view>
              </view>
              <view class="task-one-box">
                <view class="cont-title">班次：</view>
                <view class="cont-value">{{ item.shift }}</view>
              </view>
            </view>
            <!-- expired 0-正常，1-过期 -->
            <!-- taskType 0-排班，1-领料，2-调机，3-报工，4-质检，5返工，6-提醒 -->
            <view class="task-right" v-if="[0, 1, 2, 3, 4].includes(item.taskType)">
              <view
                v-if="item.taskType !== 3 && item.expired === 0"
                :class="item.confirmed === 0 && item.expired === 0 ? 'pick-button' : 'pick-button2'"
                @click="confirmTask(item)"
              >
                领取
              </view>
              <view class="taskStatus" v-if="item.expired === 1">
                <wd-img width="100%" height="100%" :src="expiredImage" />
              </view>
            </view>
          </view>
        </view>
        <view class="readStatus1" v-if="item.readStatus === 1">已读</view>
        <view class="readStatus0" v-else-if="item.readStatus === 0">未读</view>
      </view>
      <wd-loadmore :state="state" @reload="loadmore" />
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { getMessageList, confirmMessageTask } from '@/service/message/index'
import { onReachBottom } from '@dcloudio/uni-app'
import { compareWithToDay, formatDateToChinese, formatDateString } from '@/utils/time'
import expiredImage from '@/static/images/message/expired.png'
import { LoadMoreState } from 'wot-design-uni/components/wd-loadmore/types'

onReachBottom(() => {
  if (page < totalPage) {
    loadmore()
  } else {
    state.value = 'finished'
  }
})

let page = 0
const pageSize = 10
let totalPage = 1
// 消息列表数据
const messageList = ref<any[]>([])

const state = ref<LoadMoreState>('loading')

const loadmore = () => {
  queryMessageList(page++)
}

// 加载消息列表数据
const queryMessageList = (page: number) => {
  getMessageList({ page, size: pageSize })
    .then((res) => {
      // 处理数据
      console.log(res)
      messageList.value = [
        ...messageList.value,
        ...res.data.content.map((item: any) => ({
          ...item,
        })),
      ]
      const totalElements = res.data.totalElements
      totalPage = Math.ceil(totalElements / pageSize)
      console.log(totalPage)
      if (page === totalPage - 1) {
        state.value = 'finished'
      }
    })
    .catch((error) => {
      console.log(error)
      state.value = 'error'
    })
}

// 确认任务
const confirmTask = (item: any) => {
  if (item.confirmed === 0 && item.expired === 0) {
    const flag = compareWithToDay(item.scheduleDate)
    const content =
      flag === 1
        ? '该任务是' +
          formatDateToChinese(item.scheduleDate) +
          '的' +
          item.shift +
          '任务，请确认是否提前执行？'
        : '确定领取该任务？'
    uni.showModal({
      title: '提示',
      content,
      confirmText: '确定',
      cancelText: '取消',
      success: function (res) {
        if (res.confirm) {
          uni.showLoading({
            title: '领取中',
          })
          confirmMessageTask(item.id)
            .then((res) => {
              uni.hideLoading()
              if (res.data) {
                // 重置列表数据
                resetMessageList()
                uni.showToast({
                  title: '领取成功',
                  duration: 2000,
                })
              }
            })
            .catch((err) => {
              console.log(err)
              uni.hideLoading()
            })
        }
      },
    })
  }
}

const resetMessageList = () => {
  messageList.value = []
  page = 0
  queryMessageList(page++)
}

// 页面加载时自动触发第一页数据加载
onMounted(() => {
  queryMessageList(page++)
})
</script>

<style lang="scss">
.message-page {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100vh;
  overflow: hidden;
  background-color: #f3f3f3;
}

.message-box {
  flex: 1;
  width: 94%;
  margin: auto;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
  scrollbar-color: #4787ef #f3f3f3;
}

.time-sty {
  padding-top: 10px;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-size: 10px;
  font-weight: 400;
  color: #333333;
  text-align: center;
  background-color: #f3f3f3;
}

.task {
  padding: 20px;
  background-color: #ffffff;
}

.taskTopBorder {
  height: 3px;
  margin-top: 10px;
  background: #4787ef;
  border-radius: 2px 2px 0px 0px;
}

.taskTopBorder2 {
  height: 3px;
  margin-top: 10px;
  background: #e1e1e1;
  border-radius: 2px 2px 0px 0px;
}

.task-title {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.msgBox {
  width: 16px;
  height: 16px;
  margin-top: 1px;
  margin-right: 4px;
}

.task-cont {
  width: calc(100% - 20px);
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-size: 15px;
  font-weight: 400;
  color: #333333;
  text-align: left;
  word-break: break-all;
  word-wrap: break-word;
}

.task-cont-box {
  display: flex;
  width: 100%;
  margin-top: 15px;
}

.task-left {
  width: 75%;
}

.task-right {
  display: inline-flex;
  justify-content: flex-end;
  width: 25%;
}

.task-one-box {
  display: flex;
  width: 100%;
}

.cont-title {
  width: 26%;
  margin-top: 2px;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-size: 12px;
  font-weight: 400;
  color: #999999;
  text-align: left;
}

.cont-value {
  width: 72%;
  margin-top: 2px;
  margin-left: 2%;
  overflow: hidden;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-size: 12px;
  font-weight: 400;
  color: #333333;
  text-align: left;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.pick-button {
  float: right;
  width: 62px;
  height: 22px;
  margin-top: 2px;
  font-size: 10px;
  line-height: 22px;
  color: #4787ef;
  text-align: center;
  background: #ffffff;
  border: 1px solid #4787ef;
  border-radius: 11px;
}

.pick-button2 {
  float: right;
  width: 62px;
  height: 22px;
  margin-top: 2px;
  font-size: 10px;
  line-height: 22px;
  color: #e5e5e5;
  text-align: center;
  background: #ffffff;
  border: 1px solid #e5e5e5;
  border-radius: 11px;
}

.readStatus0 {
  margin-top: 5px;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-size: 8px;
  font-weight: 400;
  color: #4787ef;
  text-align: right;
}

.readStatus1 {
  margin-top: 5px;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-size: 8px;
  font-weight: 400;
  color: #e1e1e1;
  text-align: right;
}

.taskStatus {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 47px;
  max-width: 100%;
  height: 47px;
}

.list-item {
  position: relative;
  display: flex;
  padding: 10px 15px;
  color: #464646;
  background: #fff;
}

.list-item:after {
  position: absolute;
  bottom: 0;
  left: 0;
  display: block;
  width: 100%;
  height: 1px;
  content: '';
  background: #eee;
  transform: scaleY(0.5);
}
image {
  display: block;
  width: 120px;
  height: 78px;
  margin-right: 15px;
}
.right {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}
</style>
