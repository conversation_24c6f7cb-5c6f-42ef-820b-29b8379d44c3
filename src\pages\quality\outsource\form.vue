<route lang="json5">
{
  needLogin: true,
  style: {
    navigationBarTitleText: '委外检管理',
  },
}
</route>
<template>
  <view class="container">
    <!-- 当前工单信息 - 只有在扫描后显示 -->
    <view class="info-section">
      <view class="section-title">
        <view class="title-bar"></view>
        <text class="title-text">委外检信息</text>
      </view>

      <wd-cell-group title="检验人员">
        <!-- 从本地存储获取登录人信息，然后使用wd-input填入信息，并且不能修改 -->
        <wd-input v-model="inspector" placeholder="系统带出名称" readonly />
      </wd-cell-group>

      <wd-cell-group title="数量(件)">
        <wd-input v-model="quantity" type="number" placeholder="请输入数量" />
      </wd-cell-group>

      <wd-cell-group title="批次码">
        <!-- 参考completed-inspection页面的批次码扫描实现 -->
        <view class="input-with-scan">
          <wd-input v-model="erpCode" placeholder="请扫描批次码" class="scan-input" />
          <view class="scan-btn" @tap="scanBatchNo">
            <wd-icon name="scan" class="scan-icon" />
          </view>
        </view>
      </wd-cell-group>

      <wd-cell-group title="产品名称">
        <wd-input v-model="productName" placeholder="请输入产品名称" />
      </wd-cell-group>

      <wd-cell-group title="委外加工公司">
        <wd-input v-model="supplier" placeholder="请输入委外加工公司" />
      </wd-cell-group>

      <wd-cell-group title="工艺名称">
        <wd-input v-model="processName" placeholder="请输入工艺名称" />
      </wd-cell-group>

      <view class="submit-btn">
        <wd-button type="primary" block @click="handleSubmit">确认</wd-button>
      </view>
    </view>

    <!-- 已绑定信息 - 只有在扫描后显示 -->
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { postOutsource } from '@/service/outsource'
import { IOutsourceResource } from '@/service/outsource/types'
import { useUserStore } from '@/store/user'

// 控制工单信息和绑定类型信息的显示

const userStore = useUserStore()

// 检验人员，从用户store中获取当前登录用户的名称
const inspector = ref(userStore.userInfo.user.username)
// 数量
const quantity = ref()
// 批次码
const erpCode = ref()
// 产品名称
const productName = ref('')
// 供应商名称
const supplier = ref('')
// 工艺名称
const processName = ref()

const outsourceResource = ref<IOutsourceResource>()

// 扫描批次码功能，参考completed-inspection页面的实现
const scanBatchNo = () => {
  console.log('扫描批次码')
  uni.scanCode({
    success: (res) => {
      erpCode.value = res.result // 将扫描结果赋值给批次码字段
    },
    fail: (err) => {
      console.error('扫码失败:', err)
      uni.showToast({
        title: '扫码失败',
        icon: 'none',
      })
    },
  })
}

// 提交表单
const handleSubmit = async () => {
  if (!productName.value) {
    uni.showToast({
      title: '请输入产品名称',
      icon: 'none',
    })
    return
  }

  if (!erpCode.value) {
    uni.showToast({
      title: '请输入批次码',
      icon: 'none',
    })
    return
  }

  if (!quantity.value) {
    uni.showToast({
      title: '请输入数量',
      icon: 'none',
    })
    return
  }

  if (!supplier.value) {
    uni.showToast({
      title: '请输入委外加工公司',
      icon: 'none',
    })
    return
  }

  if (!processName.value) {
    uni.showToast({
      title: '请输入工艺',
      icon: 'none',
    })
    return
  }

  const resp = await postOutsource({
    processName: processName.value,
    erpCode: erpCode.value,
    supplier: supplier.value,
    inspector: inspector.value,
    quantity: quantity.value,
    productName: productName.value,
  })

  // 模拟提交成功
  uni.showLoading({
    title: '提交中...',
  })

  setTimeout(() => {
    uni.hideLoading()
    uni.showToast({
      title: '保存成功',
      icon: 'success',
      duration: 2000,
      success: () => {
        setTimeout(() => {
          // 返回上一页
          uni.navigateTo({
            url: '/pages/quality/outsource/index',
          })
        }, 2000)
      },
    })
  }, 1500)
}
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding-bottom: 120rpx;
  background-color: #f5f5f5;
}

.scan-section {
  width: 100%;
  background-color: #ffffff;
}

.scan-section-inp {
  width: 80%;
  padding: 10rpx;
  margin: 0 auto;
}

.info-section {
  width: 100%;
  padding: 30rpx;
  margin-top: 20rpx;
  background-color: #ffffff;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.title-bar {
  width: 8rpx;
  height: 36rpx;
  margin-right: 20rpx;
  background-color: #2979ff;
  border-radius: 4rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.info-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #666666;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
  word-break: break-all;
}

.form-section {
  width: 100%;
  padding: 30rpx;
  margin-top: 20rpx;
  background-color: #ffffff;
}

.form-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 30rpx;
}

.form-label {
  margin-bottom: 16rpx;
  font-size: 28rpx;
  color: #666666;
}

.form-value {
  font-size: 28rpx;
  color: #333333;
  word-break: break-all;
}

.form-input-wrapper {
  width: 100%;
}

.submit-button {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 10;
  width: 100%;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-top: 1px solid #ebedf0;
}

/* 扫描输入框样式，参考completed-inspection页面的实现 */
.input-with-scan {
  display: flex;
  align-items: center;
  padding-right: 15rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
}

.scan-input {
  flex: 1;
  background-color: transparent;
}

.scan-btn {
  padding: 10rpx;
}

.scan-icon {
  font-size: 40rpx;
  color: #4a90e2;
}
</style>
