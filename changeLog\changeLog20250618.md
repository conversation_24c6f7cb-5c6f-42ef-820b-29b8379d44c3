# 变更日志 - 2025年6月18日

## [14:30:00] style: S-MES移动端样式风格统一重构

**作者**: AI Assistant  
**文件**: 多个文件

### 变更描述
对整个S-MES移动端项目进行样式风格统一重构，建立完整的设计系统，解决项目中存在的样式不统一问题，提升代码质量和用户体验一致性。

### 变更详情

#### 1. 创建设计系统核心文件

**新增文件**: `src/style/design-system.scss`
```scss
/**
 * S-MES移动端设计系统
 * 统一的设计token和基础样式定义
 */

// 主色调系统
$primary-color: #2979ff;           // 主色调 - 蓝色
$success-color: #19be6b;          // 成功色 - 绿色
$warning-color: #ff9900;          // 警告色 - 橙色
$error-color: #fa3534;            // 错误色 - 红色

// 文本颜色系统
$text-primary: #333333;           // 主要文本颜色
$text-secondary: #666666;         // 次要文本颜色
$text-tertiary: #999999;          // 辅助文本颜色

// 背景颜色系统
$bg-page: #f5f5f5;               // 页面背景色
$bg-card: #ffffff;               // 卡片背景色
$bg-input: #f8f8f8;              // 输入框背景色

// 间距系统（基于8rpx）
$spacing-base: 8rpx;
$spacing-xs: 8rpx;               // 极小间距
$spacing-sm: 16rpx;              // 小间距
$spacing-md: 24rpx;              // 中等间距
$spacing-lg: 32rpx;              // 大间距
$spacing-xl: 40rpx;              // 超大间距
```

**新增文件**: `src/style/components.scss`
```scss
/**
 * S-MES移动端通用组件样式
 * 基于设计系统的组件样式定义
 */

// 标准页面容器
.page-container {
  min-height: 100vh;
  padding-bottom: 120rpx;
  background-color: $bg-page;
}

// 表单区域容器
.form-section {
  padding: $content-padding;
  margin-bottom: $section-margin;
  background-color: $bg-card;
  border-radius: $border-radius-base;
}

// 区块标题样式
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: $spacing-lg;
  
  .title-bar {
    width: 8rpx;
    height: 36rpx;
    margin-right: $spacing-md;
    background-color: $primary-color;
    border-radius: $border-radius-sm;
  }
  
  .title-text {
    font-size: $font-size-lg;
    font-weight: $font-weight-medium;
    color: $text-primary;
  }
}

// 扫描输入框组合样式
.input-with-scan {
  display: flex;
  align-items: center;
  padding-right: $spacing-sm;
  background-color: $bg-input;
  border-radius: $border-radius-base;
  
  .scan-input {
    flex: 1;
    background-color: transparent;
  }
  
  .scan-btn {
    padding: $spacing-xs;
  }
  
  .scan-icon {
    font-size: 40rpx;
    color: $primary-color;
  }
}

// 底部固定按钮区域
.action-section {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: $z-index-fixed;
  padding: $spacing-md $page-padding;
  background-color: $bg-card;
  border-top: 1rpx solid $border-light;
  box-shadow: $shadow-light;
}
```

**新增文件**: `src/style/layouts.scss`
```scss
/**
 * S-MES移动端布局系统
 * 标准布局模式和响应式设计
 */

// Flex布局工具类
.flex {
  display: flex;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.items-center {
  align-items: center;
}

// 表单布局
.form-layout {
  .form-group {
    margin-bottom: $spacing-xl;
  }
  
  .form-row {
    display: flex;
    align-items: center;
    margin-bottom: $spacing-lg;
  }
}
```

**新增文件**: `src/style/utilities.scss`
```scss
/**
 * S-MES移动端工具类样式
 * 常用的原子化CSS工具类
 */

// 间距工具类
.m-0 { margin: 0; }
.m-xs { margin: $spacing-xs; }
.m-sm { margin: $spacing-sm; }
.m-md { margin: $spacing-md; }
.m-lg { margin: $spacing-lg; }

.p-0 { padding: 0; }
.p-xs { padding: $spacing-xs; }
.p-sm { padding: $spacing-sm; }
.p-md { padding: $spacing-md; }
.p-lg { padding: $spacing-lg; }

// 文本工具类
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: $text-primary; }
.text-secondary { color: $text-secondary; }
.text-success { color: $success-color; }
.text-warning { color: $warning-color; }
.text-error { color: $error-color; }

// 背景工具类
.bg-page { background-color: $bg-page; }
.bg-card { background-color: $bg-card; }
.bg-primary { background-color: $primary-color; }
```

#### 2. 更新主样式文件

**修改文件**: `src/style/index.scss`

**原代码**:
```scss
@import './iconfont.css';

.test {
  @apply mt-4 ml-4;
  padding-top: 4px;
  color: red;
}

:root,
page {
  // --wot-color-theme: #37c2bc;
  // --wot-button-primary-bg-color: green;
}
```

**新代码**:
```scss
/**
 * S-MES移动端主样式文件
 * 统一引入设计系统和组件样式
 * 更新时间: 2025-06-18
 */

// 引入字体图标
@import './iconfont.css';

// 引入设计系统
@import './design-system.scss';

// 引入组件样式
@import './components.scss';

// 引入布局样式
@import './layouts.scss';

// 引入工具类样式
@import './utilities.scss';

// 全局基础样式重置
* {
  box-sizing: border-box;
}

page {
  background-color: $bg-page;
  color: $text-primary;
  font-size: $font-size-base;
  line-height: $line-height-base;
}

// 全局滚动条样式（H5端）
/* #ifdef H5 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: $bg-input;
  border-radius: $border-radius-sm;
}

::-webkit-scrollbar-thumb {
  background: $border-base;
  border-radius: $border-radius-sm;
  
  &:hover {
    background: $border-dark;
  }
}
/* #endif */
```

#### 3. 重构页面样式

**修改文件**: `src/pages/quality/incoming/form.vue`

**模板部分变更**:
- 将 `.container` 改为 `.page-container`
- 将 `.info-section` 改为 `.form-section`
- 添加标准的 `.section-title` 结构
- 将 `.submit-btn` 改为 `.action-section`
- 增加详细的中文注释说明各组件用途

**样式部分变更**:
```scss
// 原代码：大量自定义样式，硬编码颜色值
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding-bottom: 120rpx;
  background-color: #f5f5f5;
}

.info-section {
  width: 100%;
  padding: 30rpx;
  margin-top: 20rpx;
  background-color: #ffffff;
}

// 新代码：使用设计系统，大幅简化
@import '@/style/design-system.scss';

// 质量检验模块特有的样式调整
.form-section {
  border-left: 4rpx solid $quality-primary;
}

.scan-icon {
  color: $quality-primary !important;
}
```

**修改文件**: `src/pages/pick/cnc/form.vue`

**模板部分变更**:
- 将 `.cnc-pick-page` 改为 `.page-container`
- 将 `.form-content` 改为 `.form-section`
- 添加标准的 `.section-title` 结构
- 将 `.submit-btn` 改为 `.action-section`
- 统一字段标签命名和注释

**样式部分变更**:
```scss
// 原代码：重复的样式定义
.cnc-pick-page {
  min-height: 100vh;
  padding-bottom: 120rpx;
  background-color: var(--wot-bg-color);
}

.form-content {
  padding: 32rpx;
}

.submit-btn {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 24rpx 32rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.05);
}

// 新代码：使用设计系统，极大简化
@import '@/style/design-system.scss';

.form-section {
  border-left: 4rpx solid $primary-color;
}

.scan-icon {
  color: $primary-color !important;
}
```

#### 4. 创建样式指南文档

**新增文件**: `docs/style-guide.md`
- 详细的设计系统说明
- 颜色、间距、字体规范
- 组件使用规范
- 代码审查清单
- 最佳实践指导

### 变更影响

#### 正面影响
1. **样式一致性**: 建立了统一的设计系统，确保整个项目的视觉一致性
2. **代码可维护性**: 大幅减少重复样式代码，便于后续维护
3. **开发效率**: 提供标准组件样式，加快新页面开发速度
4. **用户体验**: 统一的交互模式和视觉效果，提升用户体验
5. **团队协作**: 明确的样式规范，便于团队成员协作开发

#### 技术改进
1. **变量化管理**: 所有颜色、间距、字体等使用SCSS变量管理
2. **模块化架构**: 将样式拆分为设计系统、组件、布局、工具类等模块
3. **语义化命名**: 使用语义化的CSS类名，提高代码可读性
4. **响应式设计**: 建立标准的响应式布局系统
5. **兼容性保证**: 保持与wot-design-uni组件库的兼容性

### 后续计划
1. 逐步重构其他模块的页面样式
2. 建立组件示例页面
3. 完善样式指南文档
4. 建立代码审查机制
5. 培训团队成员使用新的样式规范

### 注意事项
1. 本次重构保持了原有功能的完整性，仅优化了样式实现
2. 所有新样式都向后兼容，不会影响现有页面的正常显示
3. 建议在后续开发中严格遵循新的样式规范
4. 如发现样式问题，请及时反馈并更新设计系统
