<route lang="json5">
{
  needLogin: true,
  style: {
    navigationBarTitleText: '委外检管理',
    enablePullDownRefresh: true,
  },
}
</route>

<template>
  <view class="container">
    <view class="list-section">
      <wd-cell-group v-for="item in list" :key="item.erpCode">
        <wd-card title="委外检记录" type="rectangle">
          <wd-cell title="检验人员" :value="item.inspector" />
          <wd-cell title="物料数量" :value="item.quantity" />
          <wd-cell title="批次号" :value="item.erpCode" />
          <wd-cell title="产品名称" :value="item.productName" />
          <wd-cell title="委外加工公司" :value="item.supplier" />
          <wd-cell title="工艺名称" :value="item.processName" />
          <wd-cell title="检验时间" :value="item.createTime" />
        </wd-card>
      </wd-cell-group>
      <wd-loadmore :state="loadMoreState" @reload="loadMore" />
    </view>
    <!-- 底部按钮 -->
    <view class="bottom-button">
      <wd-button type="primary" block @click="goToForm">新增检验记录</wd-button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { IOutsourceItem } from '@/service/outsource/types'
import { getOutsourceList } from '@/service/outsource'
import { onLoad, onReachBottom } from '@dcloudio/uni-app'
import { LoadMoreState } from 'wot-design-uni/components/wd-loadmore/types'
import { useUserStore } from '@/store'

// 分页相关参数
const pageSize = 10
let currentPage = 0
let totalPage = 10
const loadMoreState = ref<LoadMoreState>('loading')

// 添加loading状态
const loading = ref(false)
// 调机列表
const list = ref<IOutsourceItem[]>([])

// 请求列表数据
const loadData = () => {
  loading.value = true
  try {
    getOutsourceList({
      inspector: useUserStore().userInfo.user.username,
      page: currentPage++,
      size: pageSize, // 参数名应该改为size
    })
      .then((res) => {
        const newData = res.data.content
        list.value = [...list.value, ...newData]
        totalPage = Math.ceil(res.data.totalElements / pageSize)
        if (currentPage === totalPage) {
          loadMoreState.value = 'finished'
        }
      })
      .catch((err) => {
        console.log(err)
        loadMoreState.value = 'error'
      })
  } catch (error) {
    loadMoreState.value = 'error'
  } finally {
    loading.value = false
  }
}

// 加载更多
const loadMore = () => {
  loadData()
}

onReachBottom(() => {
  if (currentPage < totalPage) {
    loadMore()
  } else {
    loadMoreState.value = 'finished'
  }
})

onLoad(() => {
  loadData()
})

// 前往表单页面
const goToForm = () => {
  uni.navigateTo({
    url: '/pages/quality/outsource/form',
  })
}
</script>

<style lang="scss">
.container {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  min-height: 100vh;
  padding-bottom: 120rpx; /* 为底部按钮留出空间 */
  overflow-x: hidden;
  background-color: #f5f5f5;

  & > view {
    box-sizing: border-box;
    width: 100%;
    padding: 0;
    margin: 0;
    list-style-type: none;
  }
}

.search-bar {
  position: sticky;
  top: 0;
  z-index: 10;
  box-sizing: border-box;
  width: 100%;
  padding: 20rpx 30rpx;
  margin: 0;
  background-color: #ffffff;
}

.bottom-button {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 10;
  width: 100%;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-top: 1px solid #ebedf0;
}
</style>
