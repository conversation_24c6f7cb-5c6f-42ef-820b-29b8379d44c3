# 变更日志 - 2025年05月30日

## [09:04:25] [refactor]: 优化完工检验表单与详情页UI

**作者**: 开发团队  
**文件**:

- src/pages/quality/completed-inspection/form.vue
- src/pages/quality/completed-inspection/detail.vue

### 变更描述

1. 将表单中的检验条件和条件备注从"文本+编辑按钮"改为直接使用输入框和文本域
2. 优化表单备注信息的样式，改为卡片式布局
3. 更新详情页，将原来的"检验条件备注"拆分为"检验条件"和"条件备注"两个独立区域

### 变更详情

**表单修改**:

```vue
<!-- 原检验条件 -->
<view class="info-item full-width inspection-condition-row">
  <view>
    <text>检验条件：</text>
    <text class="condition-value">{{ formData.inspectionCondition || '' }}</text>
  </view>
  <wd-icon name="edit" @tap.stop="editInspectionCondition" class="edit-icon" />
</view>

<!-- 新检验条件 -->
<view class="info-item full-width">
  <text class="info-label required-field">检验条件：</text>
  <wd-input
    v-model="formData.inspectionCondition"
    placeholder="请输入检验条件"
    clearable
  />
</view>

<!-- 原备注样式 -->
<wd-form-item label="备注" prop="remark">
  <wd-textarea
    v-model="formData.remark"
    placeholder="请输入"
    :maxlength="200"
    show-word-count
  />
</wd-form-item>

<!-- 新备注样式 -->
<view class="inspection-card">
  <view class="card-title">
    <view class="title-bar"></view>
    <text>备注信息</text>
  </view>
  <view class="card-content">
    <view class="info-item full-width">
      <wd-textarea
        v-model="formData.remark"
        placeholder="请输入备注信息"
        :maxlength="200"
        show-word-count
      />
    </view>
  </view>
</view>
```

**详情页修改**:

```vue
<!-- 原检验条件备注 -->
<view class="card-section" v-if="detailData.conditionRemark">
  <view class="section-title">检验条件备注</view>
  <view class="remark-text">{{ detailData.conditionRemark }}</view>
</view>

<!-- 新检验条件和条件备注 -->
<view class="card-section" v-if="detailData.inspectionCondition">
  <view class="section-title">检验条件</view>
  <view class="remark-text">{{ detailData.inspectionCondition }}</view>
</view>

<view class="card-section" v-if="detailData.conditionRemark">
  <view class="section-title">条件备注</view>
  <view class="remark-text">{{ detailData.conditionRemark }}</view>
</view>
```
