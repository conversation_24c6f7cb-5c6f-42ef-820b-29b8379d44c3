export interface IWorkReportBeforeRes {
  machineOrderNum: string
  machineTaskId: number
  scheduleDate: string
}

export interface IWorkOrderRes {
  machineOrderNum?: string
  machineTaskId?: number
  scheduleDate?: string
  deviceNo?: string
  deviceNumber?: string
  materialName?: string
  planQuantity?: number
  remark?: string
  shift?: string
  workReportQuantity?: number
}

export interface IWorkReportForm {
  machineTaskId: number // 工单ID
  reportQuantity: number // 报工数量
  productHours: number // 生产工时
  defQuantity: number // 不良品数量
  displayQuantity: number // 机台显示数量
  changeHours?: number // 停机工时
  shutdownRemark?: string // 停机备注
  traceCode: string // 追溯码
  nextTraceCode?: string // 新追溯码
  shutdownReason?: string // 停机原因
}

export interface IMobileParams {
  machineTaskId: number
}

export interface IMobileRes {
  startTime: string
  endTime: string
  content: IWorkReportBeforeRes[]
  changeHours: number
  productHours: number
  sum: number
  displaySum: number
  defSum: number
}

export interface IWorkReportListParams {
  page: number
  size: number
  startTime: string
  endTime: string
}

export interface IWorkReportListRes {
  content: []
  totalElements: number
}
