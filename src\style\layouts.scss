/**
 * S-MES移动端布局系统
 * 标准布局模式和响应式设计
 * 作者: AI Assistant
 * 创建时间: 2025-06-18
 */

@import './design-system.scss';

/* ==================== 基础布局 ==================== */

// 页面根容器
.layout-root {
  min-height: 100vh;
  background-color: $bg-page;
}

// 主内容区域
.layout-main {
  padding-bottom: 120rpx; // 为底部导航或按钮预留空间
}

// 内容包装器
.layout-wrapper {
  max-width: 750rpx;
  margin: 0 auto;
  padding: 0 $page-padding;
}

/* ==================== Flex布局工具类 ==================== */

// 基础flex容器
.flex {
  display: flex;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

// 对齐方式
.justify-start {
  justify-content: flex-start;
}

.justify-center {
  justify-content: center;
}

.justify-end {
  justify-content: flex-end;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.justify-evenly {
  justify-content: space-evenly;
}

.items-start {
  align-items: flex-start;
}

.items-center {
  align-items: center;
}

.items-end {
  align-items: flex-end;
}

.items-stretch {
  align-items: stretch;
}

// flex属性
.flex-1 {
  flex: 1;
}

.flex-auto {
  flex: auto;
}

.flex-none {
  flex: none;
}

/* ==================== 网格布局 ==================== */

// 两列网格
.grid-2 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: $spacing-md;
}

// 三列网格
.grid-3 {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: $spacing-md;
}

// 四列网格
.grid-4 {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: $spacing-md;
}

/* ==================== 表单布局 ==================== */

// 表单容器布局
.form-layout {
  .form-group {
    margin-bottom: $spacing-xl;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .form-row {
    display: flex;
    align-items: center;
    margin-bottom: $spacing-lg;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .form-col {
    flex: 1;
    
    &:not(:last-child) {
      margin-right: $spacing-md;
    }
  }
  
  // 表单标签布局
  .form-label-layout {
    display: flex;
    align-items: flex-start;
    
    .form-label {
      width: 160rpx;
      margin-right: $spacing-md;
      margin-bottom: 0;
      flex-shrink: 0;
    }
    
    .form-control {
      flex: 1;
    }
  }
}

/* ==================== 列表布局 ==================== */

// 列表布局容器
.list-layout {
  .list-header {
    padding: $spacing-md $page-padding;
    background-color: $bg-card;
    border-bottom: 1rpx solid $border-light;
    
    .search-bar {
      margin-bottom: $spacing-md;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    .filter-bar {
      display: flex;
      align-items: center;
      gap: $spacing-sm;
    }
  }
  
  .list-content {
    padding: $spacing-md $page-padding;
  }
  
  .list-footer {
    padding: $spacing-md $page-padding;
    text-align: center;
    background-color: $bg-card;
    border-top: 1rpx solid $border-light;
  }
}

/* ==================== 详情页布局 ==================== */

// 详情页布局
.detail-layout {
  .detail-header {
    padding: $spacing-xl $page-padding;
    background-color: $bg-card;
    border-bottom: 1rpx solid $border-light;
    
    .detail-title {
      font-size: $font-size-xl;
      font-weight: $font-weight-bold;
      color: $text-primary;
      margin-bottom: $spacing-sm;
    }
    
    .detail-subtitle {
      font-size: $font-size-base;
      color: $text-secondary;
    }
  }
  
  .detail-content {
    padding: $spacing-md $page-padding;
  }
  
  .detail-section {
    margin-bottom: $spacing-xl;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

/* ==================== 卡片布局 ==================== */

// 卡片网格布局
.card-grid {
  display: grid;
  gap: $spacing-md;
  padding: $spacing-md $page-padding;
  
  &.grid-1 {
    grid-template-columns: 1fr;
  }
  
  &.grid-2 {
    grid-template-columns: 1fr 1fr;
  }
  
  &.grid-3 {
    grid-template-columns: 1fr 1fr 1fr;
  }
}

// 卡片列表布局
.card-list {
  padding: $spacing-md $page-padding;
  
  .card-item {
    margin-bottom: $spacing-md;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

/* ==================== 固定定位布局 ==================== */

// 顶部固定区域
.fixed-top {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: $z-index-fixed;
  background-color: $bg-card;
  border-bottom: 1rpx solid $border-light;
  box-shadow: $shadow-light;
}

// 底部固定区域
.fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: $z-index-fixed;
  background-color: $bg-card;
  border-top: 1rpx solid $border-light;
  box-shadow: $shadow-light;
}

// 粘性定位
.sticky-top {
  position: sticky;
  top: 0;
  z-index: $z-index-sticky;
  background-color: $bg-card;
}

/* ==================== 响应式布局 ==================== */

// 小屏幕适配
@media (max-width: $breakpoint-sm) {
  .layout-wrapper {
    padding: 0 $spacing-md;
  }
  
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr 1fr;
  }
  
  .form-label-layout {
    flex-direction: column;
    
    .form-label {
      width: 100%;
      margin-right: 0;
      margin-bottom: $spacing-sm;
    }
  }
}

// 中等屏幕适配
@media (min-width: $breakpoint-md) {
  .layout-wrapper {
    max-width: 1200rpx;
  }
}

/* ==================== 特殊布局 ==================== */

// 居中布局
.center-layout {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}

// 空状态布局
.empty-layout {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-xxl;
  text-align: center;
  
  .empty-icon {
    font-size: 120rpx;
    color: $text-tertiary;
    margin-bottom: $spacing-lg;
  }
  
  .empty-text {
    font-size: $font-size-base;
    color: $text-secondary;
    margin-bottom: $spacing-lg;
  }
  
  .empty-action {
    // 空状态操作按钮样式
  }
}

/* ==================== 加载布局 ==================== */

// 加载状态布局
.loading-layout {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-xxl;
  
  .loading-spinner {
    margin-bottom: $spacing-lg;
  }
  
  .loading-text {
    font-size: $font-size-base;
    color: $text-secondary;
  }
}
