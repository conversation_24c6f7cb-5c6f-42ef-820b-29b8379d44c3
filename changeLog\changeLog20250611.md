# 变更日志 - 2025年6月11日

## [16:20:07] feat: 首页待办任务区展示qualityTask数据并添加领取按钮

**作者**: AI助手  
**文件**: src/pages/index/index.vue

### 变更描述

将qualityTask接口返回的任务数据渲染到首页"待办任务"区域，每条任务显示材料名称、工序、批次号、计划数量、检验时间、领取人，并为每条任务添加"领取"按钮，点击后弹出"领取成功"提示。无任务时显示原有空状态。

### 变更详情

**原代码**:

```vue
<view class="task-card">
  <view class="card-header">
    <text class="card-title">待办任务</text>
  </view>
  <view class="empty-state">
    <image
      class="empty-img"
      src="https://ai-public.mastergo.com/ai/img_res/6ef2fca8993cc395769edd81f6342999.jpg"
      mode="aspectFit"
    />
    <text class="empty-text">暂无任务待处理</text>
  </view>
</view>
```

**新代码**:

```vue
<view class="task-card">
  <view class="card-header">
    <text class="card-title">待办任务</text>
  </view>
  <template v-if="qualityTask && qualityTask.length > 0">
    <view
      class="task-item"
      v-for="item in qualityTask"
      :key="item.id"
    >
      <view class="task-row">
        <text class="task-label">材料：</text>
        <text class="task-value">{{ item.materialName }}</text>
      </view>
      <view class="task-row">
        <text class="task-label">工序：</text>
        <text class="task-value">{{ item.procedureName }}</text>
      </view>
      <view class="task-row">
        <text class="task-label">批次号：</text>
        <text class="task-value">{{ item.machineOrderNum }}</text>
      </view>
      <view class="task-row">
        <text class="task-label">计划数量：</text>
        <text class="task-value">{{ item.planQuantity }}</text>
      </view>
      <view class="task-row">
        <text class="task-label">检验时间：</text>
        <text class="task-value">{{ item.qualityTime }}</text>
      </view>
      <view class="task-row">
        <text class="task-label">领取人：</text>
        <text class="task-value">{{ item.nickName || '未领取' }}</text>
      </view>
      <view class="task-row task-btn-row">
        <wd-button type="primary" size="small" @click="handleReceive(item)">领取</wd-button>
      </view>
    </view>
  </template>
  <template v-else>
    <view class="empty-state">
      <image
        class="empty-img"
        src="https://ai-public.mastergo.com/ai/img_res/6ef2fca8993cc395769edd81f6342999.jpg"
        mode="aspectFit"
      />
      <text class="empty-text">暂无任务待处理</text>
    </view>
  </template>
</view>
```
