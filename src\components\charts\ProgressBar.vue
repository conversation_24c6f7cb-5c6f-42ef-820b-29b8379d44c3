<template>
  <view class="progress-bar-container" :style="{ width: barWidth + '%' }">
    <view
      class="progress-bar"
      :style="{
        height: barHeight,
        backgroundColor: barBackGroundColor,
      }"
    >
      <view
        class="progress-bar-inner"
        :style="{
          width: progressValue + '%',
          backgroundColor: barBackColor,
        }"
      ></view>
    </view>
  </view>
</template>

<script>
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'ProgressBar',
  props: {
    // 进度值（百分比）
    progressValue: {
      type: Number,
      default: 0,
    },
    // 进度条高度
    barHeight: {
      type: String,
      default: '12px',
    },
    // 进度条宽度（百分比）
    barWidth: {
      type: Number,
      default: 100,
    },
    // 进度条背景色
    barBackGroundColor: {
      type: String,
      default: '#F3F3F3',
    },
    // 进度条前景色
    barBackColor: {
      type: String,
      default: '#5EC691',
    },
  },
})
</script>

<style scoped>
.progress-bar-container {
  width: 100%;
}

.progress-bar {
  width: 100%;
  overflow: hidden;
  border-radius: 6px;
}

.progress-bar-inner {
  height: 100%;
  border-radius: 6px;
  transition: width 0.3s ease;
}
</style>
