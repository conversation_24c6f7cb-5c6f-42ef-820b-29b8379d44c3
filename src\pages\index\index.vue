<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  type: 'home',
  layout: 'tabbar',
  needLogin: true,
  style: {
    navationStyle: 'custom',
    navigationBarTitleText: '首页',
  },
}
</route>
<template>
  <view class="container">
    <!-- Banner区域 -->
    <view class="banner">
      <image
        class="banner-img"
        src="https://ai-public.mastergo.com/ai/img_res/a32878975ba049af4fa11a05b05609a1.jpg"
        mode="aspectFill"
      />
      <view class="banner-text">
        <text class="banner-title">欢迎使用S-MES系统</text>
        <text class="banner-subtitle">Welcome to s-mes</text>
      </view>
    </view>

    <!-- 添加内容包装器 -->
    <view class="content-wrapper">
      <!-- 功能模块入口 -->
      <view class="function-grid">
        <view
          class="grid-item"
          v-for="(item, index) in functionList"
          :key="index"
          @click="handleModuleClick(item)"
        >
          <view class="icon-wrapper" :style="{ backgroundColor: item.bgColor }">
            <wd-icon :name="item.icon" size="28" color="#FFFFFF" />
            <view v-if="item.name === '消息' && unreadCount > 0" class="badge">
              {{ unreadCount }}
            </view>
          </view>
          <text class="grid-text">{{ item.name }}</text>
        </view>
      </view>

      <!-- 当班任务区域 -->
      <view class="task-card">
        <view class="card-header">
          <text class="card-title">待办任务</text>
          <text v-if="taskList.length > 0" class="card-status">
            已完成 {{ todayTask.completed }}/{{ todayTask.total }}
          </text>
        </view>
        <template v-if="taskList.length > 0">
          <view
            class="task-item"
            v-for="item in taskList"
            :key="item.id"
            :disabled="
              !(
                item.taskType === 3 &&
                item.status === 0 &&
                item.taskStatus === 0 &&
                item.workReportFlag
              )
            "
          >
            <view class="task-row" v-if="!!item.device">
              <text class="task-label">设备号：</text>
              <text class="task-value">{{ item.device.machineNumber }}</text>
            </view>
            <view class="task-row">
              <text class="task-label">任务类型：</text>
              <text class="task-value">{{ taskTitleMap[item.taskType] }}</text>
            </view>
            <view class="task-row">
              <text class="task-label">加工单：</text>
              <text class="task-value">{{ item.machineOrder.machineOrderNum }}</text>
            </view>
            <view class="task-row task-btn-row" v-if="item.taskType === 0 || item.taskType === 4">
              <view v-if="item.status === 0">
                <wd-button type="primary" size="small" @click="handleTask(item)">处理</wd-button>
              </view>

              <view v-if="item.status === 1" @click="jumpDetail(item)">
                <image class="img-box" :src="completeImage"></image>
              </view>
            </view>
            <view
              class="task-row task-btn-row"
              v-if="item.taskType === 1 || item.taskType === 2 || item.taskType === 3"
            >
              <view v-if="item.taskStatus === 0 && item.status == 0">
                <wd-button type="primary" size="small" @click="handleTask(item)">处理</wd-button>
              </view>
              <view v-else-if="item.taskStatus === 1">
                <image class="img-box" :src="expiredImage"></image>
              </view>
              <view v-else-if="item.taskStatus === 2">
                <image class="img-box" :src="exceptionImage"></image>
              </view>
              <view
                v-else-if="item.taskStatus === 0 && item.status === 1"
                @click="jumpDetail(item)"
              >
                <image class="img-box" :src="completeImage"></image>
              </view>
            </view>
          </view>
          <wd-loadmore v-show="false" :state="state" @loadmore="loadMore" />
        </template>
        <template v-else>
          <view class="empty-state">
            <wd-img :src="noTaskImage" class="empty-img" />
            <text class="empty-text">暂无任务待处理</text>
          </view>
        </template>
      </view>
    </view>

    <!-- 动态选项弹出框 -->
    <wd-popup v-model="popupConfig.show" position="top" custom-style="padding: 30rpx;">
      <view class="popup-title">{{ popupConfig.title }}</view>
      <view class="popup-options">
        <view
          class="popup-option"
          v-for="(option, index) in popupConfig.options"
          :key="index"
          @click="handleOptionClick(option.url)"
        >
          <view class="popup-icon-wrapper" :style="{ backgroundColor: option.bgColor }">
            <wd-icon :name="option.icon" size="28" color="#FFFFFF" />
          </view>
          <text class="popup-option-text">{{ option.name }}</text>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { tabbarStore } from '@/components/fg-tabbar/tabbar'
import { useUserStore } from '@/store/user'
import { getUnreadMessageCount } from '@/service/message/index'
import { ITodayTaskRes, todayTaskList } from '@/service/arrangement/index'
import completeImage from '@/static/images/index/complete.png'
import expiredImage from '@/static/images/index/expired.png'
import exceptionImage from '@/static/images/index/exception.png'
import noTaskImage from '@/static/images/index/noTask.png'
import { compareWithToDay, formatDateToChinese } from '@/utils/time'
import { LoadMoreState } from 'wot-design-uni/components/wd-loadmore/types'
// 获取用户信息
const userStore = useUserStore()

// 设置当前tabbar索引为0（首页）
onShow(() => {
  tabbarStore.setCurIdx(0)
  getUnreadMessage(0)
  loadMore()
})

const unreadCount = ref(0) // 未读消息数量

const getUnreadMessage = (readStatus: number) => {
  getUnreadMessageCount(readStatus).then((res) => {
    unreadCount.value = res.data
    console.log('未读消息数量', res)
  })
}
const taskTitleMap = {
  0: '排班',
  1: '领料',
  2: '调机',
  3: '报工',
  4: '质检',
}
const todayTask = ref<ITodayTaskRes>()
const taskList = ref([])
const getTodayTask = () => {
  todayTaskList().then((res) => {
    console.log('今日任务', res)
    todayTask.value = res.data
    taskList.value = res.data.content
  })
}
const state = ref<LoadMoreState>('finished')
const loadMore = () => {
  getTodayTask()
}

onReachBottom(() => {
  loadMore()
})

// 定义质量角色专属功能列表
const qualityFunctionList = [
  {
    name: '来料检',
    icon: 'search',
    bgColor: '#fa3534',
    url: '/pages/quality/incoming/index',
  },
  {
    name: '委外加工检',
    icon: 'search',
    bgColor: '#ff9900',
    url: '/pages/quality/outsource/index',
  },
  {
    name: '完工检',
    icon: 'search',
    bgColor: '#19be6b',
    url: '/pages/quality/completed-inspection/index',
  },
]

// 定义调机功能列表
const machineSetupFunctionList = [
  {
    name: 'CNC调机',
    icon: 'tools',
    bgColor: '#4c6ef5',
    url: '/pages/machine/cnc/index',
  },
  {
    name: '压铸调机',
    icon: 'tools',
    bgColor: '#2979ff',
    url: '/pages/machine/casting/index',
  },
]
// 定义领料功能列表
const pickFunctionList = [
  {
    name: 'CNC领料',
    icon: 'download',
    bgColor: '#19be6b',
    url: '/pages/pick/cnc/index',
  },
  {
    name: '压铸领料',
    icon: 'download',
    bgColor: '#2979ff',
    url: '/pages/pick/pressure/index',
  },
  {
    name: '清洗领料',
    icon: 'download',
    bgColor: '#4c6ef5',
    url: '/pages/pick/clean/index',
  },
]

// 定义普通功能列表
const normalFunctionList = [
  { name: '排班', icon: 'calendar', bgColor: '#2979ff', url: '/pages/arrangement/tasks' },
  { name: '领料', icon: 'download', bgColor: '#19be6b', url: '/pages/material/pickup' },
  { name: '报工', icon: 'clock', bgColor: '#ff9900', url: '/pages/report/workList' },
  { name: '质检', icon: 'search', bgColor: '#fa3534', url: '/pages/quality/index' },
  { name: '调机', icon: 'tools', bgColor: '#fa3534', url: '/pages/machine/index' },
  // { name: '生产进度', icon: 'chart', bgColor: '#1296db', url: '/pages/production/progress' },
  { name: '消息', icon: 'notification', bgColor: '#9c27b0', url: '/pages/message/index' },
]

// 根据角色显示不同的功能列表
const functionList = computed(() => {
  // 获取所有角色名称
  const roles = userStore.userInfo?.user?.roles
  const roleNames = Array.isArray(roles) ? roles.map((role) => role.name) : []

  // 所有用户都显示通用功能列表，特殊处理逻辑在handleModuleClick中实现
  return normalFunctionList
})

// popup弹出框配置
const popupConfig = ref({
  show: false,
  title: '',
  options: [] as Array<{
    name: string
    icon: string
    bgColor: string
    url: string
  }>,
})

const handleModuleClick = (item: any) => {
  console.log('点击功能模块:', item.name)

  // 获取角色信息
  const roles = userStore.userInfo?.user?.roles
  const roleNames = Array.isArray(roles) ? roles.map((role) => role.name) : []
  const isAdmin =
    roleNames.includes('超级管理员') ||
    roleNames.includes('admin') ||
    userStore.userInfo?.user?.username === 'admin'

  // 针对"调机"模块的特殊处理
  if (item.name === '调机') {
    const hasCncRole = roleNames.includes('CNC调机师')
    const hasCastingRole = roleNames.includes('压铸调机师')

    // 如果用户同时拥有两种调机角色或是超级管理员，弹出选择框
    if ((hasCncRole && hasCastingRole) || isAdmin) {
      popupConfig.value = {
        show: true,
        title: '请选择调机类型',
        options: machineSetupFunctionList,
      }
      return // 阻止后续代码执行
    }
    // 如果只拥有CNC调机角色，直接跳转
    else if (hasCncRole) {
      uni.navigateTo({ url: '/pages/machine/cnc/index' })
      return
    }
    // 如果只拥有压铸调机角色，直接跳转
    else if (hasCastingRole) {
      uni.navigateTo({ url: '/pages/machine/casting/index' })
      return
    }
  }

  // 针对"质检"模块的特殊处理
  if (item.name === '质检') {
    const hasQualityRole = roleNames.includes('质量')

    // 如果用户拥有质量角色或是超级管理员，弹出选择框
    if (hasQualityRole || isAdmin) {
      popupConfig.value = {
        show: true,
        title: '请选择质检类型',
        options: qualityFunctionList,
      }
      return // 阻止后续代码执行
    }
  }

  if (item.name === '领料') {
    const hasPickRole = roleNames.includes('操作工')
    // 如果用户拥有质量角色或是超级管理员，弹出选择框

    if (hasPickRole || isAdmin) {
      popupConfig.value = {
        show: true,
        title: '请选择领料类型',
        options: pickFunctionList,
      }
      return // 阻止后续代码执行
    }
  }
  // 检查是否有定义URL
  if (item.url) {
    uni.navigateTo({
      url: item.url,
      fail: (err) => {
        console.error('导航失败:', err)
        // 如果导航失败，可能是页面不存在，提示用户
        uni.showToast({
          title: '功能开发中...',
          icon: 'none',
        })
      },
    })
  } else {
    uni.showToast({
      title: '功能开发中...',
      icon: 'none',
    })
  }
}

// 处理选项点击
const handleOptionClick = (url: string) => {
  popupConfig.value.show = false
  uni.navigateTo({ url })
}

// 处理任务按钮事件
const handleTask = (item: any) => {
  const flag = compareWithToDay(item.scheduleDate)
  if (flag === 1) {
    const content =
      '该任务是' +
      formatDateToChinese(item.scheduleDate) +
      '的' +
      item.shift +
      '任务，请确认是否提前执行？'
    uni.showModal({
      title: '提示',
      content,
      confirmText: '确定',
      cancelText: '取消',
      success: function (res) {
        // 点击取消时
        if (res.confirm) {
          jumpDetail(item)
        }
      },
    })
  } else {
    jumpDetail(item)
  }
}

// taskType ：0 排班，1： 领料，2：调机, 3 ：报工:, 4 ：质检
const jumpDetail = (item: any) => {
  if (item.taskType === 0) {
    const scheduleData = JSON.stringify(item.machineOrder) // 这里转换成 字符串
    uni.navigateTo({
      url: '/pages/arrangement/tasks?scheduleData=' + scheduleData,
    })
  } else if (item.taskType === 1) {
    const pickData = JSON.stringify(item.machineOrder) // 这里转换成 字符串
    uni.navigateTo({
      url: '/pages/task/picking/picking?pickData=' + pickData,
    })
  } else if (item.taskType === 2) {
    const deviceData = {
      machineOrder: JSON.parse(JSON.stringify(item.machineOrder)),
      device: {
        machineTaskId: item.machineTaskId,
        machineNumber: item.device.machineNumber,
      },
    }
    try {
      uni.removeStorageSync('deviceRun')
    } catch (e) {
    } finally {
      uni.navigateTo({
        url: '/pages/task/deviceRun/index?pickData=' + JSON.stringify(deviceData),
      })
    }
  } else if (item.taskType === 3) {
    uni.navigateTo({
      url: '/pages/report/work?taskId=' + item.taskId,
    })
  } else if (item.taskType === 4) {
    const qualityData = {
      id: item.id,
      machineNumber: item.device.machineNumber,
      reportId: item.reportId,
    }
    console.log(qualityData, 'qualityData')
    uni.navigateTo({
      url: '/pages/quality/completed-inspection/form?qualityData=' + JSON.stringify(qualityData),
    })
  }
}
</script>

<style scoped>
.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
}
/* 内容容器样式 */
.content-wrapper {
  box-sizing: border-box;
  width: 100%;
  padding: 0 30rpx;
  margin: 0 auto;
}

.banner {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 40%; /* 控制宽高比，可根据设计调整 */
  overflow: hidden;
}
/* 在平板和大屏设备上减小banner高度比例 */
@media screen and (min-width: 768px) {
  .banner {
    padding-bottom: 30%;
  }
  .content-wrapper {
    max-width: 700px;
    padding: 0 20rpx;
  }
}
@media screen and (min-width: 1024px) {
  .banner {
    padding-bottom: 25%;
  }
  .content-wrapper {
    max-width: 1200px;
    padding: 0 50rpx;
  }
}
.banner-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.banner-text {
  position: absolute;
  top: 50%;
  left: 40rpx;
  z-index: 1;
  transform: translateY(-50%);
}
.banner-title {
  display: block;
  margin-bottom: 8rpx;
  font-size: 18px;
  color: #ffffff;
}
.banner-subtitle {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}
.function-grid {
  position: relative;
  z-index: 1;
  display: flex;
  flex-wrap: wrap;
  padding: 30rpx;
  margin-top: -60rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.grid-item {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 25%;
  margin-bottom: 40rpx;
}
/* 在较小的屏幕上调整为三列布局 */
@media screen and (max-width: 375px) {
  .grid-item {
    width: 33.33%;
  }
}
/* 在平板设备上调整为五列布局 */
@media screen and (min-width: 768px) {
  .function-grid {
    padding: 40rpx;
  }
  .grid-item {
    width: 20%;
    margin-bottom: 50rpx;
  }
}
/* 在大屏设备上调整为六列布局 */
@media screen and (min-width: 1024px) {
  .function-grid {
    padding: 50rpx;
  }
  .grid-item {
    width: 16.66%;
    margin-bottom: 60rpx;
  }
}
.icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 96rpx;
  height: 96rpx;
  margin-bottom: 16rpx;
  border-radius: 24rpx;
}
.grid-text {
  width: 100%;
  overflow: hidden;
  font-size: 14px;
  color: #333333;
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.badge {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32rpx;
  height: 32rpx;
  padding: 0 6rpx;
  font-size: 12px;
  color: #ffffff;
  background-color: #ff4d4f;
  border-radius: 16rpx;
}
.task-card {
  flex: 1;
  padding: 30rpx;
  margin-top: 30rpx;
  margin-bottom: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
}
/* 在平板和大屏设备上调整任务卡片的间距 */
@media screen and (min-width: 768px) {
  .task-card {
    padding: 40rpx;
    margin-top: 40rpx;
    margin-bottom: 40rpx;
  }
}
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 24rpx;
}

.card-status {
  font-size: 28rpx;
  color: #666;
}
.card-title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
}
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
}
.empty-img {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}
.empty-text {
  font-size: 14px;
  color: #999999;
}
/* 选项弹出框样式 */
.popup-title {
  padding-bottom: 20rpx;
  margin-bottom: 20rpx;
  font-size: 16px;
  font-weight: 500;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}

.popup-options {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  padding: 20rpx 0;
}

.popup-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 30rpx;
  margin: 10rpx;
  border-radius: 12rpx;
  transition: all 0.2s;
}

.popup-option:active {
  background-color: #f5f5f5;
}

.popup-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 16rpx;
  border-radius: 24rpx;
}

.popup-option-text {
  font-size: 14px;
  color: #333333;
}

.task-item {
  padding: 24rpx;
  margin-bottom: 24rpx;
  background: #f7fafd;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(41, 121, 255, 0.04);
}
.task-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}
.task-label {
  min-width: 80rpx;
  font-size: 13px;
  color: #888;
}
.task-value {
  margin-left: 8rpx;
  font-size: 14px;
  color: #333;
}
.task-btn-row {
  justify-content: flex-end;
  margin-top: 8rpx;
  margin-bottom: 0;
}

.img-box {
  width: 47px;
  height: 47px;
}
</style>
