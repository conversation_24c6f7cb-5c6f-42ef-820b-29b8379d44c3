export function getCurrentDateString(): string {
  const now = new Date()
  return formatDate(now)
}

export function timestampsToDateStrings(timestamps: number[]): string[] {
  return timestamps.map((timestamp) => {
    const date = new Date(timestamp)
    return formatDate(date)
  })
}

/**
 * 比较两个日期字符串的大小
 * @param dateStr1 第一个日期字符串
 * @param dateStr2 第二个日期字符串
 * @returns 1: dateStr1 > dateStr2, -1: dateStr1 < dateStr2, 0: 相等
 */
export function compareDates(dateStr1: string, dateStr2: string): number {
  const date1 = new Date(dateStr1)
  const date2 = new Date(dateStr2)

  if (date1 > date2) return 1
  if (date1 < date2) return -1
  return 0
}

/**
 * 比较两个日期字符串的大小
 * @param dateStr1 第一个日期字符串
 * @param dateStr2 第二个日期字符串
 * @returns 1: dateStr1 > dateStr2, -1: dateStr1 < dateStr2, 0: 相等
 */
export function compareWithToDay(dateStr1: string): number {
  const dateStr2 = getCurrentDateString()
  return compareDates(dateStr1, dateStr2)
}

/**
 * 将日期字符串格式化为中文日期格式
 * @param dateStr 日期字符串，格式如'2025-06-9 00:00:00'
 * @returns 格式化后的中文日期字符串，如'2025年6月9日'
 */
export function formatDateToChinese(dateStr: string): string {
  const date = new Date(dateStr)
  if (isNaN(date.getTime())) {
    return dateStr // 如果日期无效，返回原字符串
  }
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  return `${year}年${month}月${day}日`
}

export function formatDateString(dateStr: string): string {
  const date = new Date(dateStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return formatDate(date)
}

export function formatDate(date: Date): string {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}
